<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NSE Option Chain Viewer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        .option-chain-table {
            font-size: 0.85rem;
        }

        .option-chain-table th {
            background-color: #f8f9fa;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .option-chain-container {
            max-height: 600px;
            overflow-y: auto;
        }

        .call-section {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .put-section {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .strike-price {
            background-color: #e9ecef;
            font-weight: bold;
        }

        .atm-strike {
            background-color: #ffcdd2 !important;
            font-weight: bold;
            color: #d32f2f;
        }

        .positive-change {
            color: #4caf50;
        }

        .negative-change {
            color: #f44336;
        }

        .in-the-money {
            background-color: rgba(255, 243, 224, 0.5);
        }

        .timestamp {
            font-size: 0.8rem;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">NSE Option Chain Viewer</h3>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <a href="/options-analysis" class="btn btn-primary">
                                <i class="bi bi-graph-up"></i> Advanced Options Analysis
                            </a>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="stockSelect" class="form-label">Select Index/Stock</label>
                                    <select class="form-select" id="stockSelect">
                                        <option value="">Loading...</option>
                                        <optgroup label="Indices">
                                            <option value="NIFTY">NIFTY</option>
                                            <option value="BANKNIFTY">BANKNIFTY</option>
                                            <option value="FINNIFTY">FINNIFTY</option>
                                            <option value="MIDCPNIFTY">MIDCPNIFTY</option>
                                        </optgroup>
                                        <optgroup label="Stocks" id="stocksGroup">
                                            <option value="">Loading stocks...</option>
                                        </optgroup>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="expirySelect" class="form-label">Select Expiry Date</label>
                                    <select class="form-select" id="expirySelect" disabled>
                                        <option value="">Select a stock first</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div id="stockInfo" class="alert alert-info d-none">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 id="stockSymbol" class="mb-0"></h5>
                                    <small>Underlying Value: <span id="underlyingValue"></span></small>
                                    <div class="timestamp" id="timestamp"></div>
                                </div>
                                <div>
                                    <button id="downloadBtn" class="btn btn-sm btn-success d-none">Download CSV</button>
                                    <button id="refreshBtn" class="btn btn-sm btn-primary ms-2">Refresh Data</button>
                                </div>
                            </div>
                        </div>

                        <div id="loadingIndicator" class="text-center my-4 d-none">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Fetching option chain data... This may take a few moments.</p>
                        </div>

                        <div id="errorMessage" class="alert alert-danger d-none"></div>

                        <div id="optionChainContainer" class="option-chain-container d-none">
                            <table class="table table-bordered table-striped option-chain-table">
                                <!-- No headers as requested -->
                                <tbody id="optionChainBody">
                                    <!-- Option chain data will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const stockSelect = document.getElementById('stockSelect');
            const expirySelect = document.getElementById('expirySelect');
            const stockInfo = document.getElementById('stockInfo');
            const stockSymbol = document.getElementById('stockSymbol');
            const underlyingValue = document.getElementById('underlyingValue');
            const timestamp = document.getElementById('timestamp');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const errorMessage = document.getElementById('errorMessage');
            const optionChainContainer = document.getElementById('optionChainContainer');
            const optionChainBody = document.getElementById('optionChainBody');
            const downloadBtn = document.getElementById('downloadBtn');
            const refreshBtn = document.getElementById('refreshBtn');

            let currentFilePath = '';

            // Format numbers with commas
            function formatNumber(num) {
                return new Intl.NumberFormat('en-IN').format(num);
            }

            // Format decimal numbers
            function formatDecimal(num, decimals = 2) {
                return parseFloat(num).toFixed(decimals);
            }

            // Load list of NSE stocks
            async function loadStocks() {
                try {
                    const response = await fetch('/api/nse-stocks');
                    const data = await response.json();

                    const stocksGroup = document.getElementById('stocksGroup');

                    if (data.success && data.stocks && data.stocks.length > 0) {
                        // Clear the loading option
                        stocksGroup.innerHTML = '';

                        // Filter out indices as they're already in the dropdown
                        const indices = ['NIFTY', 'BANKNIFTY', 'FINNIFTY', 'MIDCPNIFTY'];
                        const stocks = data.stocks.filter(stock => !indices.includes(stock));

                        // Add stocks to the stocks optgroup
                        stocks.forEach(stock => {
                            const option = document.createElement('option');
                            option.value = stock;
                            option.textContent = stock;
                            stocksGroup.appendChild(option);
                        });
                    } else {
                        // Add some default stocks if API fails
                        stocksGroup.innerHTML = '';
                        const defaultStocks = [
                            "RELIANCE", "TCS", "HDFCBANK", "INFY", "ICICIBANK",
                            "HINDUNILVR", "SBIN", "BHARTIARTL", "KOTAKBANK", "ITC"
                        ];

                        defaultStocks.forEach(stock => {
                            const option = document.createElement('option');
                            option.value = stock;
                            option.textContent = stock;
                            stocksGroup.appendChild(option);
                        });

                        console.warn('Using default stock list due to API failure');
                    }
                } catch (error) {
                    console.error('Error loading NSE stocks:', error);

                    // Add some default stocks if API fails
                    const stocksGroup = document.getElementById('stocksGroup');
                    stocksGroup.innerHTML = '';
                    const defaultStocks = [
                        "RELIANCE", "TCS", "HDFCBANK", "INFY", "ICICIBANK"
                    ];

                    defaultStocks.forEach(stock => {
                        const option = document.createElement('option');
                        option.value = stock;
                        option.textContent = stock;
                        stocksGroup.appendChild(option);
                    });
                }
            }

            // Load option chain data
            async function loadOptionChain(symbol, expiryDate = null) {
                showLoading(true);
                hideError();
                optionChainContainer.classList.add('d-none');

                try {
                    let url = `/api/nse-option-chain?symbol=${encodeURIComponent(symbol)}`;
                    if (expiryDate) {
                        url += `&expiry_date=${encodeURIComponent(expiryDate)}`;
                    }

                    const response = await fetch(url);
                    const data = await response.json();

                    if (data.success && data.data) {
                        // Update stock info
                        stockSymbol.textContent = data.symbol;
                        underlyingValue.textContent = data.underlying_value;

                        // Display timestamp if available
                        if (data.timestamp) {
                            timestamp.textContent = `Last updated: ${data.timestamp}`;
                        } else {
                            timestamp.textContent = `Last updated: ${new Date().toLocaleString()}`;
                        }

                        stockInfo.classList.remove('d-none');

                        // Set download link
                        if (data.file_path) {
                            currentFilePath = data.file_path;
                            downloadBtn.classList.remove('d-none');
                        } else {
                            downloadBtn.classList.add('d-none');
                        }

                        // Populate expiry dates if not already set
                        if (!expiryDate && data.expiry_dates && data.expiry_dates.length > 0) {
                            populateExpiryDates(data.expiry_dates);
                        }

                        // Render option chain
                        renderOptionChain(data.data, data.underlying_value);

                    } else {
                        showError(data.message || 'Failed to fetch option chain data');
                    }
                } catch (error) {
                    showError('Error fetching option chain: ' + error.message);
                } finally {
                    showLoading(false);
                }
            }

            // Populate expiry dates dropdown
            function populateExpiryDates(dates) {
                expirySelect.innerHTML = '<option value="">All Expiry Dates</option>';

                dates.forEach(date => {
                    const option = document.createElement('option');
                    option.value = date;
                    option.textContent = date;
                    expirySelect.appendChild(option);
                });

                expirySelect.disabled = false;
            }

            // Render option chain table
            function renderOptionChain(data, spotPrice) {
                optionChainBody.innerHTML = '';

                if (!data || data.length === 0) {
                    showError('No option chain data available');
                    return;
                }

                // Sort by strike price
                data.sort((a, b) => a['Strike Price'] - b['Strike Price']);

                // Find the closest strike price to the spot price (ATM)
                let closestStrike = data[0]['Strike Price'];
                let minDiff = Math.abs(closestStrike - spotPrice);

                data.forEach(option => {
                    const strikePrice = parseFloat(option['Strike Price']);
                    const diff = Math.abs(strikePrice - spotPrice);
                    if (diff < minDiff) {
                        minDiff = diff;
                        closestStrike = strikePrice;
                    }
                });

                // Add header row
                const headerRow = document.createElement('tr');
                headerRow.innerHTML = `
                    <td colspan="9" class="text-center bg-primary text-white">CALLS</td>
                    <td class="text-center bg-dark text-white">STRIKE</td>
                    <td colspan="9" class="text-center bg-danger text-white">PUTS</td>
                `;
                optionChainBody.appendChild(headerRow);

                // Add column headers row
                const columnHeaderRow = document.createElement('tr');
                columnHeaderRow.innerHTML = `
                    <td>OI</td>
                    <td>CHNG IN OI</td>
                    <td>VOLUME</td>
                    <td>IV</td>
                    <td>LTP</td>
                    <td>CHNG</td>
                    <td>BID QTY</td>
                    <td>BID</td>
                    <td>ASK</td>
                    <td class="text-center">STRIKE</td>
                    <td>BID QTY</td>
                    <td>BID</td>
                    <td>ASK</td>
                    <td>ASK QTY</td>
                    <td>CHNG</td>
                    <td>LTP</td>
                    <td>IV</td>
                    <td>VOLUME</td>
                    <td>CHNG IN OI</td>
                    <td>OI</td>
                `;
                optionChainBody.appendChild(columnHeaderRow);

                // Render each row
                data.forEach(option => {
                    const row = document.createElement('tr');

                    // Determine if this is the ATM strike
                    const strikePrice = parseFloat(option['Strike Price']);
                    const isATM = option['Is ATM'] || Math.abs(strikePrice - spotPrice) < 0.01 || strikePrice === closestStrike;

                    // Determine if call or put is in the money
                    const callInTheMoney = strikePrice < spotPrice;
                    const putInTheMoney = strikePrice > spotPrice;

                    // Call section
                    row.innerHTML += `
                        <td class="call-section ${callInTheMoney ? 'in-the-money' : ''}">${formatNumber(option['Call OI'] || 0)}</td>
                        <td class="call-section ${callInTheMoney ? 'in-the-money' : ''}">${formatNumber(option['Call Change in OI'] || 0)}</td>
                        <td class="call-section ${callInTheMoney ? 'in-the-money' : ''}">${formatNumber(option['Call Volume'] || 0)}</td>
                        <td class="call-section ${callInTheMoney ? 'in-the-money' : ''}">${formatDecimal(option['Call IV'] || 0)}%</td>
                        <td class="call-section ${callInTheMoney ? 'in-the-money' : ''}">${formatDecimal(option['Call LTP'] || 0)}</td>
                        <td class="call-section ${callInTheMoney ? 'in-the-money' : ''} ${(option['Call Change'] || 0) > 0 ? 'positive-change' : (option['Call Change'] || 0) < 0 ? 'negative-change' : ''}">${formatDecimal(option['Call Change'] || 0)}</td>
                        <td class="call-section ${callInTheMoney ? 'in-the-money' : ''}">${formatNumber(option['Call Bid Qty'] || 0)}</td>
                        <td class="call-section ${callInTheMoney ? 'in-the-money' : ''}">${formatDecimal(option['Call Bid Price'] || 0)}</td>
                        <td class="call-section ${callInTheMoney ? 'in-the-money' : ''}">${formatDecimal(option['Call Ask Price'] || 0)}</td>
                    `;

                    // Strike price - highlight ATM strike in red
                    row.innerHTML += `<td class="text-center ${isATM ? 'atm-strike' : 'strike-price'}">${option['Strike Price']}</td>`;

                    // Put section - rearranged to match the header
                    row.innerHTML += `
                        <td class="put-section ${putInTheMoney ? 'in-the-money' : ''}">${formatNumber(option['Put Bid Qty'] || 0)}</td>
                        <td class="put-section ${putInTheMoney ? 'in-the-money' : ''}">${formatDecimal(option['Put Bid Price'] || 0)}</td>
                        <td class="put-section ${putInTheMoney ? 'in-the-money' : ''}">${formatDecimal(option['Put Ask Price'] || 0)}</td>
                        <td class="put-section ${putInTheMoney ? 'in-the-money' : ''}">${formatNumber(option['Put Ask Qty'] || 0)}</td>
                        <td class="put-section ${putInTheMoney ? 'in-the-money' : ''} ${(option['Put Change'] || 0) > 0 ? 'positive-change' : (option['Put Change'] || 0) < 0 ? 'negative-change' : ''}">${formatDecimal(option['Put Change'] || 0)}</td>
                        <td class="put-section ${putInTheMoney ? 'in-the-money' : ''}">${formatDecimal(option['Put LTP'] || 0)}</td>
                        <td class="put-section ${putInTheMoney ? 'in-the-money' : ''}">${formatDecimal(option['Put IV'] || 0)}%</td>
                        <td class="put-section ${putInTheMoney ? 'in-the-money' : ''}">${formatNumber(option['Put Volume'] || 0)}</td>
                        <td class="put-section ${putInTheMoney ? 'in-the-money' : ''}">${formatNumber(option['Put Change in OI'] || 0)}</td>
                        <td class="put-section ${putInTheMoney ? 'in-the-money' : ''}">${formatNumber(option['Put OI'] || 0)}</td>
                    `;

                    optionChainBody.appendChild(row);
                });

                // Scroll to the ATM strike
                setTimeout(() => {
                    const atmElement = document.querySelector('.atm-strike');
                    if (atmElement) {
                        atmElement.scrollIntoView({ block: 'center', behavior: 'smooth' });
                    }
                }, 100);

                optionChainContainer.classList.remove('d-none');
            }

            // Show/hide loading indicator
            function showLoading(show) {
                if (show) {
                    loadingIndicator.classList.remove('d-none');
                } else {
                    loadingIndicator.classList.add('d-none');
                }
            }

            // Show error message
            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.classList.remove('d-none');
            }

            // Hide error message
            function hideError() {
                errorMessage.classList.add('d-none');
            }

            // Event listeners
            stockSelect.addEventListener('change', function() {
                const symbol = this.value;

                if (symbol) {
                    // Reset expiry select
                    expirySelect.innerHTML = '<option value="">Loading expiry dates...</option>';
                    expirySelect.disabled = true;

                    // Load option chain for selected stock
                    loadOptionChain(symbol);
                } else {
                    // Reset UI
                    expirySelect.innerHTML = '<option value="">Select a stock first</option>';
                    expirySelect.disabled = true;
                    stockInfo.classList.add('d-none');
                    optionChainContainer.classList.add('d-none');
                    hideError();
                }
            });

            expirySelect.addEventListener('change', function() {
                const symbol = stockSelect.value;
                const expiryDate = this.value;

                if (symbol) {
                    loadOptionChain(symbol, expiryDate);
                }
            });

            downloadBtn.addEventListener('click', function() {
                if (currentFilePath) {
                    window.location.href = `/download/${currentFilePath}`;
                }
            });

            refreshBtn.addEventListener('click', function() {
                const symbol = stockSelect.value;
                const expiryDate = expirySelect.value;

                if (symbol) {
                    loadOptionChain(symbol, expiryDate);
                }
            });

            // Initialize
            loadStocks();
        });
    </script>
</body>
</html>
