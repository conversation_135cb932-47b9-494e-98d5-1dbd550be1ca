"""
Gemini 2.0 AI Client for Options Analysis
Integrates with Google's Gemini 2.0 Flash model for advanced options trading analysis
"""

import os
import json
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional


class GeminiOptionsAnalyzer:
    """
    AI-powered options analysis using Gemini 2.0 Flash model
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Gemini AI client
        
        Args:
            api_key (str, optional): Gemini API key. If not provided, will use environment variable.
        """
        self.api_key = api_key or os.environ.get('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError("Gemini API key is required. Set GEMINI_API_KEY environment variable or pass api_key parameter.")
        
        # Configure Gemini API endpoint
        self.api_endpoint = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={self.api_key}"
        self.headers = {
            'Content-Type': 'application/json'
        }
        
        # Trading analysis parameters
        self.trading_parameters = {
            "gamma_acceleration": {
                "description": "Focus on options with high gamma potential for rapid price acceleration",
                "selection_criteria": {
                    "beta": "> 1.5",
                    "oi": "> 50k",
                    "otm_distance": "15-25%",
                    "premium_range": "₹0.05-₹0.20"
                }
            },
            "technical_indicators": {
                "rsi": "Relative Strength Index for momentum",
                "adx": "Average Directional Index for trend strength",
                "ema_crossover": "20/50 EMA crossover signals"
            },
            "risk_management": {
                "position_sizing": "Based on volatility and account size",
                "stop_loss": "Technical and time-based stops",
                "profit_booking": "Systematic profit taking levels"
            }
        }
    
    def analyze_option_chain(self, options_data: List[Dict], current_price: float, 
                           symbol: str, days_to_expiry: Optional[int] = None) -> Dict[str, Any]:
        """
        Analyze option chain data using Gemini 2.0 AI model
        
        Args:
            options_data (List[Dict]): Raw option chain data from Fyers API
            current_price (float): Current price of the underlying
            symbol (str): Symbol being analyzed
            days_to_expiry (int, optional): Days to expiry
            
        Returns:
            Dict[str, Any]: Comprehensive AI analysis results
        """
        try:
            # Prepare the analysis prompt
            prompt = self._create_analysis_prompt(options_data, current_price, symbol, days_to_expiry)
            
            # Generate AI analysis using HTTP request
            payload = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": prompt
                            }
                        ]
                    }
                ]
            }

            response = requests.post(self.api_endpoint, headers=self.headers, json=payload, timeout=60)

            if response.status_code == 200:
                response_data = response.json()
                ai_text = response_data.get('candidates', [{}])[0].get('content', {}).get('parts', [{}])[0].get('text', '')
                # Parse and structure the response
                analysis_result = self._parse_ai_response(ai_text)
            else:
                raise Exception(f"Gemini API error: {response.status_code} - {response.text}")
            
            # Add metadata
            analysis_result['metadata'] = {
                'symbol': symbol,
                'current_price': current_price,
                'days_to_expiry': days_to_expiry,
                'analysis_timestamp': datetime.now().isoformat(),
                'ai_model': 'gemini-2.0-flash',
                'data_points_analyzed': len(options_data)
            }
            
            return analysis_result
            
        except Exception as e:
            return {
                'success': False,
                'error': f"AI analysis failed: {str(e)}",
                'fallback_analysis': self._generate_fallback_analysis(options_data, current_price)
            }
    
    def _create_analysis_prompt(self, options_data: List[Dict], current_price: float, 
                              symbol: str, days_to_expiry: Optional[int]) -> str:
        """
        Create a comprehensive analysis prompt for Gemini 2.0
        """
        # Prepare option chain data summary
        data_summary = self._summarize_option_data(options_data, current_price)
        
        prompt = f"""
You are an expert options trader and quantitative analyst. Analyze the following real-time option chain data for {symbol} and provide comprehensive trading insights.

**CURRENT MARKET DATA:**
- Symbol: {symbol}
- Current Price: ₹{current_price}
- Days to Expiry: {days_to_expiry or 'Not specified'}
- Analysis Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**OPTION CHAIN DATA SUMMARY:**
{data_summary}

**ANALYSIS REQUIREMENTS:**
Please provide a detailed analysis covering these specific areas:

1. **MAX PAIN ANALYSIS:**
   - Calculate the max pain point (strike where option writers face least loss)
   - Analyze the significance of current price vs max pain
   - Provide directional bias based on max pain theory

2. **PUT-CALL RATIO (PCR) ANALYSIS:**
   - Calculate PCR based on Open Interest and Volume
   - Interpret the PCR value (bullish/bearish/neutral)
   - Compare with historical PCR ranges

3. **OPEN INTEREST ANALYSIS:**
   - Identify significant OI changes and their implications
   - Analyze OI distribution across strikes
   - Detect institutional activity patterns

4. **GAMMA EXPOSURE ANALYSIS:**
   - Estimate gamma exposure at key strikes
   - Identify potential gamma squeeze levels
   - Analyze dealer positioning implications

5. **IMPLIED VOLATILITY ANALYSIS:**
   - Assess current IV levels vs historical ranges
   - Identify IV skew patterns
   - Recommend volatility-based strategies

6. **TRADE RECOMMENDATIONS:**
   Apply gamma acceleration strategy with these criteria:
   - Focus on OTM options 15-25% from current price
   - Premium range ₹0.05-₹0.20
   - High OI (>50k preferred)
   - Strong technical confirmation
   
   Provide specific trade setups with:
   - Entry levels
   - Stop loss levels
   - Profit targets
   - Risk-reward ratios

**OUTPUT FORMAT:**
Please structure your response as a JSON object with the following format:
{{
    "success": true,
    "max_pain_analysis": {{
        "max_pain_strike": number,
        "current_vs_max_pain": "analysis text",
        "directional_bias": "Bullish/Bearish/Neutral"
    }},
    "pcr_analysis": {{
        "pcr_oi": number,
        "pcr_volume": number,
        "interpretation": "analysis text",
        "bias": "Bullish/Bearish/Neutral"
    }},
    "oi_analysis": {{
        "significant_changes": [],
        "institutional_activity": "analysis text",
        "key_levels": []
    }},
    "gamma_analysis": {{
        "gamma_exposure_levels": [],
        "squeeze_potential": "analysis text",
        "dealer_positioning": "analysis text"
    }},
    "iv_analysis": {{
        "current_iv_percentile": number,
        "iv_skew": "analysis text",
        "volatility_regime": "High/Medium/Low"
    }},
    "trade_recommendations": [
        {{
            "type": "Call/Put",
            "strike": number,
            "entry_price": number,
            "stop_loss": number,
            "target": number,
            "risk_reward": number,
            "rationale": "detailed explanation"
        }}
    ],
    "overall_bias": "Bullish/Bearish/Neutral",
    "confidence_level": number,
    "key_insights": []
}}

Ensure all numerical values are realistic and based on the actual data provided. Focus on actionable insights for gamma acceleration trading strategy.
"""
        
        return prompt
    
    def _summarize_option_data(self, options_data: List[Dict], current_price: float) -> str:
        """
        Create a concise summary of option chain data for the AI prompt
        """
        if not options_data:
            return "No option data available"
        
        # Group by option type and calculate key metrics
        calls = [opt for opt in options_data if opt.get('Option Type') == 'CE']
        puts = [opt for opt in options_data if opt.get('Option Type') == 'PE']
        
        # Calculate total OI and volume
        total_call_oi = sum(opt.get('OI', 0) for opt in calls)
        total_put_oi = sum(opt.get('OI', 0) for opt in puts)
        total_call_volume = sum(opt.get('Volume', 0) for opt in calls)
        total_put_volume = sum(opt.get('Volume', 0) for opt in puts)
        
        # Find ATM and key strikes
        strikes = sorted(set(opt.get('Strike Price', 0) for opt in options_data if opt.get('Strike Price', 0) > 0))
        atm_strike = min(strikes, key=lambda x: abs(x - current_price)) if strikes else current_price
        
        # Get top 5 strikes by OI for calls and puts
        top_call_strikes = sorted(calls, key=lambda x: x.get('OI', 0), reverse=True)[:5]
        top_put_strikes = sorted(puts, key=lambda x: x.get('OI', 0), reverse=True)[:5]
        
        summary = f"""
**OPTION CHAIN SUMMARY:**
- Total Options: {len(options_data)}
- Strike Range: {min(strikes) if strikes else 'N/A'} to {max(strikes) if strikes else 'N/A'}
- ATM Strike: {atm_strike}

**OPEN INTEREST:**
- Total Call OI: {total_call_oi:,}
- Total Put OI: {total_put_oi:,}
- PCR (OI): {total_put_oi/total_call_oi if total_call_oi > 0 else 'N/A'}

**VOLUME:**
- Total Call Volume: {total_call_volume:,}
- Total Put Volume: {total_put_volume:,}
- PCR (Volume): {total_put_volume/total_call_volume if total_call_volume > 0 else 'N/A'}

**TOP CALL STRIKES BY OI:**
{self._format_top_strikes(top_call_strikes)}

**TOP PUT STRIKES BY OI:**
{self._format_top_strikes(top_put_strikes)}
"""
        
        return summary
    
    def _format_top_strikes(self, strikes: List[Dict]) -> str:
        """Format top strikes for display"""
        if not strikes:
            return "No data available"
        
        formatted = []
        for strike in strikes:
            formatted.append(f"  {strike.get('Strike Price', 0)}: OI={strike.get('OI', 0):,}, Vol={strike.get('Volume', 0):,}, LTP=₹{strike.get('LTP', 0)}")
        
        return "\n".join(formatted)
    
    def _parse_ai_response(self, response_text: str) -> Dict[str, Any]:
        """
        Parse and validate AI response
        """
        try:
            # Try to extract JSON from the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                parsed_response = json.loads(json_str)
                
                # Validate required fields
                required_fields = ['success', 'max_pain_analysis', 'pcr_analysis', 'trade_recommendations']
                for field in required_fields:
                    if field not in parsed_response:
                        parsed_response[field] = self._get_default_field_value(field)
                
                return parsed_response
            else:
                # If no JSON found, create structured response from text
                return self._create_structured_response_from_text(response_text)
                
        except json.JSONDecodeError:
            # Fallback: create structured response from text
            return self._create_structured_response_from_text(response_text)
    
    def _create_structured_response_from_text(self, text: str) -> Dict[str, Any]:
        """
        Create structured response when JSON parsing fails
        """
        return {
            'success': True,
            'ai_analysis_text': text,
            'max_pain_analysis': {'analysis': 'See AI analysis text'},
            'pcr_analysis': {'analysis': 'See AI analysis text'},
            'oi_analysis': {'analysis': 'See AI analysis text'},
            'gamma_analysis': {'analysis': 'See AI analysis text'},
            'iv_analysis': {'analysis': 'See AI analysis text'},
            'trade_recommendations': [],
            'overall_bias': 'Neutral',
            'confidence_level': 50,
            'key_insights': ['See detailed AI analysis text above']
        }
    
    def _get_default_field_value(self, field: str) -> Any:
        """Get default value for missing fields"""
        defaults = {
            'success': True,
            'max_pain_analysis': {'analysis': 'Analysis not available'},
            'pcr_analysis': {'analysis': 'Analysis not available'},
            'oi_analysis': {'analysis': 'Analysis not available'},
            'gamma_analysis': {'analysis': 'Analysis not available'},
            'iv_analysis': {'analysis': 'Analysis not available'},
            'trade_recommendations': [],
            'overall_bias': 'Neutral',
            'confidence_level': 50
        }
        return defaults.get(field, 'Not available')
    
    def _generate_fallback_analysis(self, options_data: List[Dict], current_price: float) -> Dict[str, Any]:
        """
        Generate basic fallback analysis when AI fails
        """
        return {
            'success': False,
            'message': 'AI analysis unavailable, using basic analysis',
            'basic_analysis': {
                'total_options': len(options_data),
                'current_price': current_price,
                'analysis_time': datetime.now().isoformat()
            }
        }
