"""
Watchlist management system for batch analysis of stocks
"""

import json
import os

class WatchlistManager:
    def __init__(self):
        """Initialize the watchlist manager"""
        self.watchlists = {}
        self.default_watchlists = {
            'NIFTY50': [
                'RELIANCE-EQ', 'TCS-EQ', 'HDFCBANK-EQ', 'INFY-EQ', 'ICICIBANK-EQ',
                'HINDUNILVR-EQ', 'ITC-EQ', 'SBIN-EQ', 'BHARTIARTL-EQ', 'KOTAKBANK-EQ',
                'LT-EQ', 'AXISBANK-EQ', 'ASIANPAINT-EQ', 'MARUTI-EQ', 'HCLTECH-EQ',
                'SUNPHARMA-EQ', 'BAJFINANCE-EQ', 'TITAN-EQ', 'ULTRACEMCO-EQ', 'NTPC-EQ',
                'TATAMOTORS-EQ', 'ADANIENT-EQ', 'POWERGRID-EQ', 'JSWSTEEL-EQ', 'NESTLEIND-EQ',
                'BAJAJFINSV-EQ', 'TATASTEEL-EQ', 'TECHM-EQ', 'WIPRO-EQ', 'GRASIM-EQ',
                'ADANIPORTS-EQ', 'HDFCLIFE-EQ', 'DIVISLAB-EQ', 'INDUSINDBK-EQ', 'APOLLOHOSP-EQ',
                'CIPLA-EQ', 'DRREDDY-EQ', 'COALINDIA-EQ', 'SBILIFE-EQ', 'BRITANNIA-EQ',
                'HINDALCO-EQ', 'ONGC-EQ', 'EICHERMOT-EQ', 'TATACONSUM-EQ', 'HEROMOTOCO-EQ',
                'BAJAJ-AUTO-EQ', 'M&M-EQ', 'UPL-EQ', 'BPCL-EQ'
            ],
            'FNO': [
                'RELIANCE-EQ', 'TCS-EQ', 'HDFCBANK-EQ', 'INFY-EQ', 'ICICIBANK-EQ',
                'HINDUNILVR-EQ', 'ITC-EQ', 'SBIN-EQ', 'BHARTIARTL-EQ', 'KOTAKBANK-EQ',
                'LT-EQ', 'AXISBANK-EQ', 'ASIANPAINT-EQ', 'MARUTI-EQ', 'HCLTECH-EQ',
                'SUNPHARMA-EQ', 'BAJFINANCE-EQ', 'TITAN-EQ', 'ULTRACEMCO-EQ', 'NTPC-EQ',
                'TATAMOTORS-EQ', 'ADANIENT-EQ', 'POWERGRID-EQ', 'JSWSTEEL-EQ', 'NESTLEIND-EQ',
                'BAJAJFINSV-EQ', 'TATASTEEL-EQ', 'TECHM-EQ', 'WIPRO-EQ', 'GRASIM-EQ',
                'ADANIPORTS-EQ', 'HDFCLIFE-EQ', 'DIVISLAB-EQ', 'INDUSINDBK-EQ', 'APOLLOHOSP-EQ',
                'CIPLA-EQ', 'DRREDDY-EQ', 'COALINDIA-EQ', 'SBILIFE-EQ', 'BRITANNIA-EQ',
                'HINDALCO-EQ', 'ONGC-EQ', 'EICHERMOT-EQ', 'TATACONSUM-EQ', 'HEROMOTOCO-EQ',
                'BAJAJ-AUTO-EQ', 'M&M-EQ', 'UPL-EQ', 'BPCL-EQ',
                # Additional F&O stocks beyond Nifty 50
                'AUROPHARMA-EQ', 'BANDHANBNK-EQ', 'BANKBARODA-EQ', 'BIOCON-EQ', 'BOSCHLTD-EQ',
                'CADILAHC-EQ', 'CANBK-EQ', 'CHOLAFIN-EQ', 'COLPAL-EQ', 'CONCOR-EQ',
                'CUMMINSIND-EQ', 'DLF-EQ', 'FEDERALBNK-EQ', 'GODREJCP-EQ', 'HAVELLS-EQ',
                'HDFCAMC-EQ', 'IBULHSGFIN-EQ', 'ICICIPRULI-EQ', 'JINDALSTEL-EQ', 'JUBLFOOD-EQ',
                'LUPIN-EQ', 'MARICO-EQ', 'MCDOWELL-N-EQ', 'MUTHOOTFIN-EQ', 'NMDC-EQ',
                'PEL-EQ', 'PFC-EQ', 'PIDILITIND-EQ', 'PNB-EQ', 'RECLTD-EQ',
                'SAIL-EQ', 'SIEMENS-EQ', 'TORNTPHARM-EQ', 'UBL-EQ', 'VEDL-EQ'
            ],
            'INDICES': [
                'NIFTY50-INDEX', 'BANKNIFTY-INDEX', 'FINNIFTY-INDEX'
            ],
            'CUSTOM': []
        }
        
        # Load watchlists from file if it exists
        self.watchlists_file = 'watchlists.json'
        self.load_watchlists()
    
    def load_watchlists(self):
        """Load watchlists from file"""
        try:
            if os.path.exists(self.watchlists_file):
                with open(self.watchlists_file, 'r') as f:
                    self.watchlists = json.load(f)
            else:
                # Initialize with default watchlists
                self.watchlists = self.default_watchlists
                self.save_watchlists()
        except Exception as e:
            print(f"Error loading watchlists: {str(e)}")
            # Initialize with default watchlists
            self.watchlists = self.default_watchlists
    
    def save_watchlists(self):
        """Save watchlists to file"""
        try:
            with open(self.watchlists_file, 'w') as f:
                json.dump(self.watchlists, f, indent=4)
        except Exception as e:
            print(f"Error saving watchlists: {str(e)}")
    
    def get_watchlist(self, name):
        """Get a watchlist by name"""
        return self.watchlists.get(name, [])
    
    def get_all_watchlists(self):
        """Get all watchlists"""
        return self.watchlists
    
    def add_to_watchlist(self, name, symbol):
        """Add a symbol to a watchlist"""
        if name not in self.watchlists:
            self.watchlists[name] = []
        
        if symbol not in self.watchlists[name]:
            self.watchlists[name].append(symbol)
            self.save_watchlists()
            return True
        return False
    
    def remove_from_watchlist(self, name, symbol):
        """Remove a symbol from a watchlist"""
        if name in self.watchlists and symbol in self.watchlists[name]:
            self.watchlists[name].remove(symbol)
            self.save_watchlists()
            return True
        return False
    
    def create_watchlist(self, name, symbols=None):
        """Create a new watchlist"""
        if symbols is None:
            symbols = []
        
        if name not in self.watchlists:
            self.watchlists[name] = symbols
            self.save_watchlists()
            return True
        return False
    
    def delete_watchlist(self, name):
        """Delete a watchlist"""
        if name in self.watchlists:
            del self.watchlists[name]
            self.save_watchlists()
            return True
        return False
    
    def get_watchlist_names(self):
        """Get all watchlist names"""
        return list(self.watchlists.keys())
