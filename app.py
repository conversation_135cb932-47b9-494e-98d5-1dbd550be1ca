import os
import json
import time
import random
import uuid
import pandas as pd  # Used in the Fyers option chain API
from datetime import datetime, timedelta
from flask import Flask, render_template, request, jsonify, send_file, session
from options_analysis import OptionsAnalysis
from institutional_analysis import InstitutionalAnalysis
from watchlist_manager import WatchlistManager
from batch_analyzer import BatchAnalyzer
from gemini_ai_client import GeminiOptionsAnalyzer
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Constants
TOKEN_FILE = 'fyers_token.json'

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['OUTPUT_FOLDER'] = 'outputs'
app.config['SECRET_KEY'] = os.environ.get('FLASK_SECRET_KEY', 'default_secret_key')
app.config['SESSION_TYPE'] = 'filesystem'
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=2)  # Session lasts for 2 hours

# Global dictionary to store background tasks
background_tasks = {}

# Fyers API credentials from environment variables
FYERS_APP_ID = os.environ.get('FYERS_APP_ID', 'B5QJCSZE9E-100')
FYERS_SECRET_ID = os.environ.get('FYERS_SECRET_ID', 'T8EICCWNZZ')
FYERS_CLIENT_ID = os.environ.get('FYERS_CLIENT_ID', 'B5QJCSZE9E-100')
FYERS_ACCESS_TOKEN = os.environ.get('FYERS_ACCESS_TOKEN', '')

# Gemini AI configuration
GEMINI_API_KEY = os.environ.get('GEMINI_API_KEY', 'AIzaSyB2kCcHsk0SS-BJNmFZE5RrE_lCQUfizv4')

# Create necessary directories
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)

# Create a directory for storing tokens
os.makedirs('tokens', exist_ok=True)

# Path to store the access token
TOKEN_FILE = os.path.join('tokens', 'fyers_token.json')

def get_fyers_client():
    """Get an initialized Fyers API client"""
    try:
        # Get access token - first try environment variable (for production), then file (for development)
        access_token = os.environ.get('FYERS_ACCESS_TOKEN')

        # If not in environment, try to get from file
        if not access_token and os.environ.get('FLASK_ENV') != 'production':
            if not os.path.exists(TOKEN_FILE):
                print("No access token found in file")
                return None

            with open(TOKEN_FILE, 'r') as f:
                token_data = json.load(f)

            access_token = token_data.get('access_token')

        if not access_token:
            print("No access token found")
            return None

        # Initialize the FyersModel instance
        from fyers_apiv3 import fyersModel
        return fyersModel.FyersModel(
            client_id=FYERS_CLIENT_ID,
            token=access_token,
            is_async=False,
            log_path=""
        )
    except Exception as e:
        print(f"Error initializing Fyers client: {str(e)}")
        return None

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/download/<path:filename>')
def download_file(filename):
    # Construct the full path to the file
    file_path = os.path.join(app.config['OUTPUT_FOLDER'], filename)

    # Check if the file exists
    if not os.path.exists(file_path):
        return jsonify({"success": False, "message": f"File not found: {filename}"}), 404

    # Return the file for download
    return send_file(file_path, as_attachment=True, download_name=filename)

# Removed option chain page route

@app.route('/token-manager')
def token_manager():
    """Render the token manager page"""
    # Check if token file exists
    token_exists = os.path.exists(TOKEN_FILE)
    token_data = None

    if token_exists:
        try:
            with open(TOKEN_FILE, 'r') as f:
                token_data = json.load(f)
                # Add timestamp in human-readable format
                if 'timestamp' in token_data:
                    token_time = datetime.fromtimestamp(token_data['timestamp'])
                    token_data['timestamp_readable'] = token_time.strftime('%Y-%m-%d %H:%M:%S')
        except:
            token_data = None

    return render_template('token_manager.html', token_exists=token_exists, token_data=token_data)

@app.route('/update-token', methods=['POST'])
def update_token():
    """Update the access token manually"""
    access_token = request.form.get('access_token', '')

    if not access_token:
        return jsonify({"success": False, "message": "No access token provided"})

    try:
        # In production, we'll use the token from environment variables
        # But we'll still save it to a file for development/testing
        if os.environ.get('FLASK_ENV') == 'production':
            # In production, we'll just verify the token is valid but not save it
            # The actual token will be set as an environment variable in Render
            return jsonify({"success": True, "message": "Token verified successfully (using environment variable in production)"})
        else:
            # Save the token to a file (only in development)
            with open(TOKEN_FILE, 'w') as f:
                json.dump({
                    "access_token": access_token,
                    "timestamp": time.time()
                }, f)

            return jsonify({"success": True, "message": "Token updated successfully"})
    except Exception as e:
        return jsonify({"success": False, "message": f"Error updating token: {str(e)}"})

@app.route('/generate-access-token', methods=['POST'])
def generate_access_token():
    """Generate access token from auth code"""
    try:
        data = request.json
        auth_code = data.get('auth_code', '').strip()

        if not auth_code:
            return jsonify({"success": False, "message": "Auth code is required"})

        # Use the configured credentials
        client_id = FYERS_APP_ID
        secret_key = FYERS_SECRET_ID

        print(f"Generating access token with auth_code: {auth_code[:10]}...")

        # Create session model
        from fyers_apiv3 import fyersModel

        session = fyersModel.SessionModel(
            client_id=client_id,
            secret_key=secret_key,
            redirect_uri="https://trade.fyers.in/api-login/redirect-to-app",
            response_type="code",
            grant_type="authorization_code"
        )

        # Set the auth code
        session.set_token(auth_code)

        # Generate access token
        response = session.generate_token()

        if response and response.get('s') == 'ok':
            access_token = response.get('access_token')

            if access_token:
                # Save the token
                with open(TOKEN_FILE, 'w') as f:
                    json.dump({
                        "access_token": access_token,
                        "timestamp": time.time()
                    }, f)

                print(f"Access token generated successfully: {access_token[:20]}...")

                return jsonify({
                    "success": True,
                    "message": "Access token generated successfully",
                    "access_token": access_token
                })
            else:
                return jsonify({"success": False, "message": "No access token in response"})
        else:
            error_msg = response.get('message', 'Unknown error') if response else 'No response from API'
            print(f"Token generation failed: {response}")
            return jsonify({"success": False, "message": f"Token generation failed: {error_msg}"})

    except Exception as e:
        print(f"Error generating access token: {str(e)}")
        return jsonify({"success": False, "message": f"Error generating access token: {str(e)}"})

@app.route('/enhanced-token-manager')
def enhanced_token_manager():
    """Render the enhanced token manager page"""
    return render_template('enhanced_token_manager.html')

@app.route('/api/get-auth-url', methods=['GET'])
def get_auth_url():
    """Get the correct Fyers authorization URL"""
    try:
        from fyers_apiv3 import fyersModel

        # Create session model to get the correct auth URL
        session = fyersModel.SessionModel(
            client_id=FYERS_APP_ID,
            secret_key=FYERS_SECRET_ID,
            redirect_uri="https://trade.fyers.in/api-login/redirect-to-app",
            response_type="code",
            grant_type="authorization_code"
        )

        # Generate the auth URL
        auth_url = session.generate_authcode()

        return jsonify({
            "success": True,
            "auth_url": auth_url,
            "message": "Authorization URL generated successfully"
        })

    except Exception as e:
        print(f"Error generating auth URL: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error generating auth URL: {str(e)}"
        })

@app.route('/api/fyers-option-chain', methods=['GET'])
def fetch_fyers_option_chain():
    """Fetch Fyers option chain data for a specific symbol"""
    try:
        symbol = request.args.get('symbol')
        strike_count = int(request.args.get('strike_count', 5))

        if not symbol:
            return jsonify({"success": False, "message": "Symbol is required"})

        # Get Fyers client
        fyers = get_fyers_client()
        if not fyers:
            return jsonify({"success": False, "message": "Failed to initialize Fyers client"})

        # Generate a unique filename
        filename = f"fyers_option_chain_{symbol.replace(':', '_')}_{int(time.time())}.csv"
        output_file = os.path.join(app.config['OUTPUT_FOLDER'], filename)

        # Prepare the request data
        data = {
            "symbol": symbol,
            "strikecount": strike_count,
            "timestamp": ""
        }

        # Make the API call
        print(f"Requesting option chain for {symbol} with strike count {strike_count}")
        response = fyers.optionchain(data=data)

        if not response or response.get('s') != 'ok':
            error_message = response.get('message', 'Unknown error') if response else 'No response from API'
            return jsonify({"success": False, "message": f"Failed to fetch option chain data: {error_message}"})

        # Process the response
        api_data = response['data']

        # Extract the underlying value (index/stock price)
        underlying_value = 0
        for item in api_data.get('optionsChain', []):
            if 'strike_price' in item and item['strike_price'] == -1:
                underlying_value = item.get('ltp', 0)
                break

        # Get expiry dates
        expiry_dates = [item['date'] for item in api_data.get('expiryData', [])]

        # Process options data
        options_data = []
        options_chain = api_data.get('optionsChain', [])
        print(f"Number of options in chain: {len(options_chain)}")

        # Check if we have any options data
        if not options_chain:
            return jsonify({
                "success": False,
                "message": "No options data available from Fyers API. Please check your connection and try again."
            })

        # Track if we have real OI data
        has_real_oi_data = False

        for item in options_chain:
            # Skip the underlying symbol
            if 'strike_price' in item and item['strike_price'] == -1:
                continue

            # Debug print for option data
            if 'option_type' in item:
                print(f"Processing option: {item.get('symbol', 'Unknown')} - Type: {item.get('option_type', 'Unknown')}")

            # Check if this option has real OI data
            oi_value = item.get('oi', 0)
            oi_change = item.get('oich', 0)

            if oi_value > 0 or oi_change != 0:
                has_real_oi_data = True
                print(f"Found real OI data: OI={oi_value}, OI Change={oi_change}")

            # Add the option data
            option_data = {
                'Strike Price': item.get('strike_price', 0),
                'Option Type': item.get('option_type', ''),
                'Symbol': item.get('symbol', ''),
                'OI': oi_value,
                'Change in OI': oi_change,  # Using 'oich' consistently for OI change
                'Volume': item.get('volume', 0),
                'LTP': item.get('ltp', 0),
                'Change': item.get('ltpch', 0),
                'Bid': item.get('bid', 0),
                'Ask': item.get('ask', 0)
            }

            # Print detailed option data for debugging
            print(f"Option data: {option_data}")
            options_data.append(option_data)

        # Check if we have real OI data
        if not has_real_oi_data:
            print("WARNING: No real OI data found in the options chain. This may affect analysis results.")

        # Save to CSV
        import pandas as pd
        df = pd.DataFrame(options_data)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        csv_filename = f"{symbol.replace(':', '_')}_{timestamp}.csv"
        csv_path = os.path.join(app.config['OUTPUT_FOLDER'], csv_filename)
        df.to_csv(csv_path, index=False)
        print(f"Saved options data to {csv_path}")

        # Fetch money flow data if available
        money_flow_data = None
        try:
            # Use the same fyers client that was used for the option chain request
            money_flow_data = fetch_money_flow_data(symbol, fyers)
            print(f"Money flow data fetched: {money_flow_data['success']}")
        except Exception as e:
            print(f"Error fetching money flow data: {str(e)}")
            # Continue without money flow data
            money_flow_data = {
                "success": False,
                "message": f"Error fetching money flow data: {str(e)}"
            }

        return jsonify({
            "success": True,
            "data": options_data,
            "underlying_value": underlying_value,
            "symbol": symbol,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "expiry_dates": expiry_dates,
            "money_flow": money_flow_data,
            "csv_file": csv_filename,
            "has_real_oi_data": has_real_oi_data
        })

    except Exception as e:
        return jsonify({"success": False, "message": f"Error fetching option chain: {str(e)}"})

@app.route('/api/fyers-stocks', methods=['GET'])
def get_fyers_stocks():
    """Get list of stocks available for Fyers option chain"""
    try:
        # Common indices - using the correct format based on testing
        indices = ["NSE:NIFTY50-INDEX", "NSE:FINNIFTY-INDEX", "NSE:MIDCPNIFTY-INDEX"]

        # Comprehensive list of F&O stocks - using the correct format based on testing
        stocks = [
            # Nifty 50 stocks
            "NSE:RELIANCE-EQ", "NSE:TCS-EQ", "NSE:HDFCBANK-EQ", "NSE:INFY-EQ", "NSE:ICICIBANK-EQ",
            "NSE:HINDUNILVR-EQ", "NSE:SBIN-EQ", "NSE:BHARTIARTL-EQ", "NSE:KOTAKBANK-EQ", "NSE:ITC-EQ",
            "NSE:AXISBANK-EQ", "NSE:ASIANPAINT-EQ", "NSE:MARUTI-EQ", "NSE:TATAMOTORS-EQ", "NSE:SUNPHARMA-EQ",
            "NSE:WIPRO-EQ", "NSE:BAJFINANCE-EQ", "NSE:HCLTECH-EQ", "NSE:ADANIENT-EQ", "NSE:ULTRACEMCO-EQ",
            "NSE:BAJAJFINSV-EQ", "NSE:TITAN-EQ", "NSE:NTPC-EQ", "NSE:POWERGRID-EQ", "NSE:ONGC-EQ",
            "NSE:GRASIM-EQ", "NSE:INDUSINDBK-EQ", "NSE:JSWSTEEL-EQ", "NSE:ADANIPORTS-EQ", "NSE:HDFCLIFE-EQ",
            "NSE:SBILIFE-EQ", "NSE:TECHM-EQ", "NSE:APOLLOHOSP-EQ", "NSE:BRITANNIA-EQ", "NSE:NESTLEIND-EQ",
            "NSE:DIVISLAB-EQ", "NSE:CIPLA-EQ", "NSE:DRREDDY-EQ", "NSE:EICHERMOT-EQ", "NSE:HEROMOTOCO-EQ",
            "NSE:HINDALCO-EQ", "NSE:LTIM-EQ", "NSE:LT-EQ", "NSE:M&M-EQ", "NSE:TATACONSUM-EQ", "NSE:TATASTEEL-EQ",
            "NSE:UPL-EQ", "NSE:COALINDIA-EQ", "NSE:BPCL-EQ", "NSE:BAJAJ-AUTO-EQ",

            # Additional F&O stocks
            "NSE:AUROPHARMA-EQ", "NSE:BANDHANBNK-EQ", "NSE:BANKBARODA-EQ", "NSE:BERGEPAINT-EQ", "NSE:BIOCON-EQ",
            "NSE:BOSCHLTD-EQ", "NSE:BSOFT-EQ", "NSE:CANBK-EQ", "NSE:CHOLAFIN-EQ", "NSE:COLPAL-EQ",
            "NSE:CONCOR-EQ", "NSE:COFORGE-EQ", "NSE:DABUR-EQ", "NSE:DEEPAKNTR-EQ", "NSE:DLF-EQ",
            "NSE:FEDERALBNK-EQ", "NSE:GODREJCP-EQ", "NSE:GODREJPROP-EQ", "NSE:GRANULES-EQ", "NSE:GUJGASLTD-EQ",
            "NSE:HAVELLS-EQ", "NSE:HDFCAMC-EQ", "NSE:HDFC-EQ", "NSE:HINDPETRO-EQ", "NSE:IBULHSGFIN-EQ",
            "NSE:ICICIPRULI-EQ", "NSE:IDEA-EQ", "NSE:INDIAMART-EQ", "NSE:INDIGO-EQ", "NSE:INDUSTOWER-EQ",
            "NSE:IRCTC-EQ", "NSE:JINDALSTEL-EQ", "NSE:JUBLFOOD-EQ", "NSE:LALPATHLAB-EQ", "NSE:LICHSGFIN-EQ",
            "NSE:LUPIN-EQ", "NSE:MARICO-EQ", "NSE:MCDOWELL-N-EQ", "NSE:METROPOLIS-EQ", "NSE:MFSL-EQ",
            "NSE:MGL-EQ", "NSE:MINDTREE-EQ", "NSE:MOTHERSON-EQ", "NSE:MPHASIS-EQ", "NSE:MRF-EQ",
            "NSE:MUTHOOTFIN-EQ", "NSE:NAM-INDIA-EQ", "NSE:NAUKRI-EQ", "NSE:NAVINFLUOR-EQ", "NSE:NMDC-EQ",
            "NSE:OBEROIRLTY-EQ", "NSE:PEL-EQ", "NSE:PERSISTENT-EQ", "NSE:PETRONET-EQ", "NSE:PFC-EQ",
            "NSE:PIDILITIND-EQ", "NSE:PIIND-EQ", "NSE:PNB-EQ", "NSE:POLYCAB-EQ", "NSE:RBLBANK-EQ",
            "NSE:RECLTD-EQ", "NSE:SAIL-EQ", "NSE:SBICARD-EQ", "NSE:SIEMENS-EQ", "NSE:SRF-EQ",
            "NSE:SRTRANSFIN-EQ", "NSE:SUNTV-EQ", "NSE:TATACOMM-EQ", "NSE:TATAPOWER-EQ", "NSE:TORNTPHARM-EQ",
            "NSE:TORNTPOWER-EQ", "NSE:TRENT-EQ", "NSE:VEDL-EQ", "NSE:VOLTAS-EQ", "NSE:ZEEL-EQ"
        ]

        # Combine indices and stocks
        all_symbols = indices + stocks

        return jsonify({"success": True, "stocks": all_symbols})
    except Exception as e:
        return jsonify({"success": False, "message": f"Error fetching stocks: {str(e)}"})

@app.route('/api/nse-stocks', methods=['GET'])
def get_nse_stocks():
    """Get list of stocks available for NSE option chain (fallback for compatibility)"""
    try:
        # Common indices
        indices = ["NIFTY", "BANKNIFTY", "FINNIFTY", "MIDCPNIFTY"]

        # Comprehensive list of F&O stocks for NSE format
        stocks = [
            # Nifty 50 stocks
            "RELIANCE", "TCS", "HDFCBANK", "INFY", "ICICIBANK",
            "HINDUNILVR", "SBIN", "BHARTIARTL", "KOTAKBANK", "ITC",
            "AXISBANK", "ASIANPAINT", "MARUTI", "TATAMOTORS", "SUNPHARMA",
            "WIPRO", "BAJFINANCE", "HCLTECH", "ADANIENT", "ULTRACEMCO",
            "BAJAJFINSV", "TITAN", "NTPC", "POWERGRID", "ONGC",
            "GRASIM", "INDUSINDBK", "JSWSTEEL", "ADANIPORTS", "HDFCLIFE",
            "SBILIFE", "TECHM", "APOLLOHOSP", "BRITANNIA", "NESTLEIND",
            "DIVISLAB", "CIPLA", "DRREDDY", "EICHERMOT", "HEROMOTOCO",
            "HINDALCO", "LTIM", "LT", "M&M", "TATACONSUM", "TATASTEEL",
            "UPL", "COALINDIA", "BPCL", "BAJAJ-AUTO",

            # Additional F&O stocks
            "AUROPHARMA", "BANDHANBNK", "BANKBARODA", "BERGEPAINT", "BIOCON",
            "BOSCHLTD", "BSOFT", "CANBK", "CHOLAFIN", "COLPAL",
            "CONCOR", "COFORGE", "DABUR", "DEEPAKNTR", "DLF",
            "FEDERALBNK", "GODREJCP", "GODREJPROP", "GRANULES", "GUJGASLTD",
            "HAVELLS", "HDFCAMC", "HDFC", "HINDPETRO", "IBULHSGFIN",
            "ICICIPRULI", "IDEA", "INDIAMART", "INDIGO", "INDUSTOWER",
            "IRCTC", "JINDALSTEL", "JUBLFOOD", "LALPATHLAB", "LICHSGFIN",
            "LUPIN", "MARICO", "MCDOWELL-N", "METROPOLIS", "MFSL",
            "MGL", "MINDTREE", "MOTHERSON", "MPHASIS", "MRF",
            "MUTHOOTFIN", "NAM-INDIA", "NAUKRI", "NAVINFLUOR", "NMDC",
            "OBEROIRLTY", "PEL", "PERSISTENT", "PETRONET", "PFC",
            "PIDILITIND", "PIIND", "PNB", "POLYCAB", "RBLBANK",
            "RECLTD", "SAIL", "SBICARD", "SIEMENS", "SRF",
            "SRTRANSFIN", "SUNTV", "TATACOMM", "TATAPOWER", "TORNTPHARM",
            "TORNTPOWER", "TRENT", "VEDL", "VOLTAS", "ZEEL"
        ]

        # Combine indices and stocks
        all_symbols = indices + stocks

        return jsonify({"success": True, "stocks": all_symbols})
    except Exception as e:
        return jsonify({"success": False, "message": f"Error fetching stocks: {str(e)}"})

@app.route('/options-analysis')
def options_analysis_page():
    """Render the options analysis page"""
    return render_template('options_analysis.html')

@app.route('/ai-options-analysis')
def ai_options_analysis_page():
    """Render the AI-powered options analysis page"""
    return render_template('ai_options_analysis.html')

@app.route('/api/options-analysis', methods=['POST'])
def analyze_options():
    """Perform advanced options analysis on option chain data"""
    try:
        # Get request data
        data = request.json
        options_data = data.get('options_data', [])
        current_price = float(data.get('current_price', 0))
        days_to_expiry = data.get('days_to_expiry')

        if days_to_expiry and isinstance(days_to_expiry, str):
            try:
                # Try to parse days_to_expiry from a date string
                expiry_date = datetime.strptime(days_to_expiry, '%d-%m-%Y')
                current_date = datetime.now()
                days_to_expiry = (expiry_date - current_date).days
                if days_to_expiry < 0:
                    days_to_expiry = 0
            except:
                # If parsing fails, use None (default value)
                days_to_expiry = None

        if not options_data:
            return jsonify({"success": False, "message": "No options data provided"})

        # Initialize the options analyzer
        analyzer = OptionsAnalysis()

        # Run the analysis
        analysis_results = analyzer.run_all_analysis(options_data, current_price, days_to_expiry)

        return jsonify({
            "success": True,
            "analysis": analysis_results
        })
    except Exception as e:
        return jsonify({"success": False, "message": f"Error performing options analysis: {str(e)}"})

@app.route('/api/ai-options-analysis', methods=['POST'])
def ai_analyze_options():
    """Perform AI-powered options analysis using Gemini 2.0"""
    try:
        # Get request data
        data = request.json
        options_data = data.get('options_data', [])
        current_price = float(data.get('current_price', 0))
        symbol = data.get('symbol', 'Unknown')
        days_to_expiry = data.get('days_to_expiry')

        # Parse days_to_expiry if it's a string
        if days_to_expiry and isinstance(days_to_expiry, str):
            try:
                expiry_date = datetime.strptime(days_to_expiry, '%d-%m-%Y')
                current_date = datetime.now()
                days_to_expiry = (expiry_date - current_date).days
                if days_to_expiry < 0:
                    days_to_expiry = 0
            except:
                days_to_expiry = None

        if not options_data:
            return jsonify({"success": False, "message": "No options data provided"})

        if current_price <= 0:
            return jsonify({"success": False, "message": "Invalid current price"})

        # Check if AI analyzer is available
        if not ai_analyzer:
            return jsonify({
                "success": False,
                "message": "AI analyzer not available. Please check Gemini API configuration."
            })

        # Perform AI analysis
        print(f"Starting AI analysis for {symbol} with {len(options_data)} options")
        ai_results = ai_analyzer.analyze_option_chain(
            options_data=options_data,
            current_price=current_price,
            symbol=symbol,
            days_to_expiry=days_to_expiry
        )

        # Also run traditional analysis for comparison
        traditional_analyzer = OptionsAnalysis()
        traditional_results = traditional_analyzer.run_all_analysis(options_data, current_price, days_to_expiry)

        return jsonify({
            "success": True,
            "ai_analysis": ai_results,
            "traditional_analysis": traditional_results,
            "analysis_type": "AI-Powered with Traditional Comparison"
        })

    except Exception as e:
        print(f"Error in AI analysis: {str(e)}")
        return jsonify({"success": False, "message": f"Error performing AI analysis: {str(e)}"})

@app.route('/api/trade-recommendations', methods=['POST'])
def get_trade_recommendations():
    """Generate trade recommendations based on options analysis with integrated institutional analysis"""
    try:
        # Get request data
        data = request.json
        options_data = data.get('options_data', [])
        current_price = data.get('current_price', 0)
        symbol = data.get('symbol', 'Unknown')
        days_to_expiry = data.get('days_to_expiry')
        include_institutional = data.get('include_institutional', False)  # Default to False
        money_flow_data = data.get('money_flow', None)

        # Log the request data for debugging
        print(f"Trade recommendations request: symbol={symbol}, current_price={current_price}, options_data length={len(options_data) if options_data else 0}")

        # Validate current_price
        try:
            current_price = float(current_price)
            if current_price <= 0:
                return jsonify({"success": False, "message": "Invalid current price. Please fetch option chain data first."})
        except (ValueError, TypeError):
            return jsonify({"success": False, "message": "Invalid current price. Please fetch option chain data first."})

        # Parse days_to_expiry if it's a string
        if days_to_expiry and isinstance(days_to_expiry, str):
            try:
                # Try to parse days_to_expiry from a date string
                expiry_date = datetime.strptime(days_to_expiry, '%d-%m-%Y')
                current_date = datetime.now()
                days_to_expiry = (expiry_date - current_date).days
                if days_to_expiry < 0:
                    days_to_expiry = 0
            except:
                # If parsing fails, use None (default value)
                days_to_expiry = None
                print(f"Failed to parse expiry date: {days_to_expiry}")

        # Validate options_data
        if not options_data or len(options_data) == 0:
            return jsonify({"success": False, "message": "No options data provided. Please fetch option chain data first."})

        # Check if options_data has the required fields
        required_fields = ['Strike Price', 'Option Type', 'OI', 'Volume', 'LTP']
        for option in options_data[:5]:  # Check first 5 options
            missing_fields = [field for field in required_fields if field not in option]
            if missing_fields:
                print(f"Missing required fields in options data: {missing_fields}")
                return jsonify({
                    "success": False,
                    "message": f"Options data is missing required fields: {', '.join(missing_fields)}. Please fetch option chain data first."
                })

        # Check if we have valid data in the options
        valid_data_count = 0
        for option in options_data:
            if option.get('LTP', 0) > 0 and option.get('Volume', 0) > 0:
                valid_data_count += 1

        if valid_data_count < 5:  # Need at least 5 valid options
            print(f"Not enough valid options data: only {valid_data_count} options with valid price and volume")
            return jsonify({
                "success": False,
                "message": "Not enough valid options data. Please ensure you have real-time data from Fyers API."
            })

        # Initialize the options analyzer
        analyzer = OptionsAnalysis()

        # Run options analysis first to get the analysis results
        analysis_results = analyzer.run_all_analysis(options_data, current_price, days_to_expiry)

        # Store analysis results for use in recommendations
        trade_analysis_results = analysis_results

        # Run institutional analysis if requested
        institutional_analysis = None
        if include_institutional:
            try:
                # Use global historical max pain data
                global historical_max_pain

                # Run institutional analyses with proper error handling
                volume_oi_results = {"success": False, "message": "Analysis not run"}
                dealer_gamma_results = {"success": False, "message": "Analysis not run"}
                iv_skew_results = {"success": False, "message": "Analysis not run"}
                delta_weighted_oi_results = {"success": False, "message": "Analysis not run"}
                liquidity_depth_results = {"success": False, "message": "Analysis not run"}
                smart_money_results = {"success": False, "message": "Analysis not run"}
                max_pain_shift_results = {"success": False, "message": "Analysis not run"}

                try:
                    volume_oi_results = institutional_analyzer.detect_volume_oi_spikes(options_data)
                except Exception as e:
                    print(f"Error in volume-OI analysis: {str(e)}")

                try:
                    dealer_gamma_results = institutional_analyzer.calculate_dealer_gamma(options_data, current_price)
                except Exception as e:
                    print(f"Error in dealer gamma analysis: {str(e)}")

                try:
                    iv_skew_results = institutional_analyzer.analyze_iv_skew(options_data, current_price)
                except Exception as e:
                    print(f"Error in IV skew analysis: {str(e)}")

                try:
                    delta_weighted_oi_results = institutional_analyzer.calculate_delta_weighted_oi(options_data, current_price)
                except Exception as e:
                    print(f"Error in delta-weighted OI analysis: {str(e)}")

                try:
                    liquidity_depth_results = institutional_analyzer.analyze_liquidity_depth(options_data)
                except Exception as e:
                    print(f"Error in liquidity depth analysis: {str(e)}")

                try:
                    smart_money_results = institutional_analyzer.detect_smart_money(options_data)
                except Exception as e:
                    print(f"Error in smart money analysis: {str(e)}")

                try:
                    max_pain_shift_results = institutional_analyzer.track_max_pain_shift(options_data, current_price, historical_max_pain)
                    # Update historical data for future use
                    if max_pain_shift_results.get('success', False):
                        historical_max_pain = max_pain_shift_results.get('historical_max_pain', historical_max_pain)
                except Exception as e:
                    print(f"Error in max pain shift analysis: {str(e)}")

                # Combine all analyses
                combined_analysis = ""
                if volume_oi_results.get('success', False):
                    combined_analysis += "VOLUME-OI ANALYSIS: " + volume_oi_results.get('summary', '') + "\n\n"
                if dealer_gamma_results.get('success', False):
                    combined_analysis += "DEALER GAMMA ANALYSIS: " + dealer_gamma_results.get('analysis', '') + "\n\n"
                if iv_skew_results.get('success', False):
                    combined_analysis += "IV SKEW ANALYSIS: " + iv_skew_results.get('analysis', '') + "\n\n"
                if delta_weighted_oi_results.get('success', False):
                    combined_analysis += "DELTA-WEIGHTED OI ANALYSIS: " + delta_weighted_oi_results.get('analysis', '') + "\n\n"
                if liquidity_depth_results.get('success', False):
                    combined_analysis += "LIQUIDITY DEPTH ANALYSIS: " + liquidity_depth_results.get('analysis', '') + "\n\n"
                if smart_money_results.get('success', False):
                    combined_analysis += "SMART MONEY ANALYSIS: " + smart_money_results.get('analysis', '') + "\n\n"
                if max_pain_shift_results.get('success', False):
                    combined_analysis += "MAX PAIN SHIFT ANALYSIS: " + max_pain_shift_results.get('analysis', '') + "\n\n"

                institutional_analysis = {
                    "success": True,
                    "volume_oi_spikes": volume_oi_results if volume_oi_results.get('success', False) else None,
                    "dealer_gamma": dealer_gamma_results if dealer_gamma_results.get('success', False) else None,
                    "iv_skew": iv_skew_results if iv_skew_results.get('success', False) else None,
                    "delta_weighted_oi": delta_weighted_oi_results if delta_weighted_oi_results.get('success', False) else None,
                    "liquidity_depth": liquidity_depth_results if liquidity_depth_results.get('success', False) else None,
                    "smart_money": smart_money_results if smart_money_results.get('success', False) else None,
                    "max_pain_shift": max_pain_shift_results if max_pain_shift_results.get('success', False) else None,
                    "combined_analysis": combined_analysis
                }
            except Exception as e:
                print(f"Error running institutional analysis: {str(e)}")
                # Continue without institutional analysis if it fails
                institutional_analysis = None

        # Generate trade recommendations with institutional analysis and money flow data if available
        try:
            print(f"Generating trade recommendations for {symbol} with {len(options_data)} options")
            recommendations = analyzer.generate_trade_recommendations(
                options_data,
                current_price,
                symbol,
                days_to_expiry,
                institutional_analysis,
                money_flow_data
            )

            if not recommendations:
                print("No recommendations returned from generate_trade_recommendations")
                return jsonify({
                    "success": False,
                    "message": "No valid trade recommendations could be generated. Please ensure you have real-time data from Fyers API."
                })
        except Exception as e:
            print(f"Error generating trade recommendations: {str(e)}")
            return jsonify({
                "success": False,
                "message": f"Error generating trade recommendations: {str(e)}"
            })

        # Enhance recommendations with money flow data if available
        money_flow_enhanced = False
        try:
            if money_flow_data and money_flow_data.get('success', False):
                print(f"Enhancing recommendations with money flow data for {symbol}")
                recommendations = incorporate_money_flow_into_recommendations(
                    recommendations,
                    money_flow_data,
                    trade_analysis_results
                )
                # Check if money flow was enhanced
                money_flow_enhanced = True
                print("Money flow enhancement successful")
        except Exception as e:
            print(f"Error enhancing recommendations with money flow data: {str(e)}")
            # Continue without money flow enhancement if it fails
            money_flow_enhanced = False

        # Final validation of recommendations
        if not recommendations or not isinstance(recommendations, dict):
            print(f"Invalid recommendations object: {recommendations}")
            return jsonify({
                "success": False,
                "message": "Failed to generate valid trade recommendations. Please ensure you have real-time data from Fyers API."
            })

        # Check if we have any recommendations
        has_calls = 'calls' in recommendations and recommendations['calls'] and len(recommendations['calls']) > 0
        has_puts = 'puts' in recommendations and recommendations['puts'] and len(recommendations['puts']) > 0
        has_best_trade = 'best_trade' in recommendations and recommendations['best_trade'] and isinstance(recommendations['best_trade'], dict) and len(recommendations['best_trade']) > 0

        if not (has_calls or has_puts or has_best_trade):
            print("No valid recommendations found in the result")
            return jsonify({
                "success": False,
                "message": "No valid trade recommendations found. Please ensure you have real-time data from Fyers API."
            })

        # Return the successful response
        print(f"Successfully generated recommendations for {symbol}")
        return jsonify({
            "success": True,
            "recommendations": recommendations,
            "institutional_analysis_included": institutional_analysis is not None,
            "money_flow_enhanced": money_flow_enhanced
        })
    except Exception as e:
        return jsonify({"success": False, "message": f"Error performing options analysis: {str(e)}"})

# Initialize the institutional analysis module
institutional_analyzer = InstitutionalAnalysis()

# Initialize the watchlist manager
watchlist_manager = WatchlistManager()

# Initialize the batch analyzer
batch_analyzer = BatchAnalyzer()

# Initialize the AI analyzer
try:
    ai_analyzer = GeminiOptionsAnalyzer(api_key=GEMINI_API_KEY)
    print("Gemini AI analyzer initialized successfully")
except Exception as e:
    print(f"Warning: Failed to initialize Gemini AI analyzer: {e}")
    ai_analyzer = None

# Storage for historical max pain data
historical_max_pain = {
    'dates': [],
    'values': [],
    'price': []
}

def incorporate_money_flow_into_recommendations(recommendations, money_flow_data, analysis_results):
    """
    Incorporate money flow data into trade recommendations to enhance accuracy

    Args:
        recommendations (dict): Original trade recommendations
        money_flow_data (dict): Money flow data including institutional activity
        analysis_results (dict): Options analysis results

    Returns:
        dict: Enhanced recommendations with money flow insights
    """
    try:
        # Extract money flow bias and strength
        flow_bias = money_flow_data.get('flow_bias', 'Neutral')
        flow_strength = money_flow_data.get('flow_strength', 'Unknown')

        # Get original recommendations
        best_trades = recommendations.get('best_trades', [])
        alternate_trades = recommendations.get('alternate_trades', [])

        # Check if money flow aligns with option chain analysis
        options_bias = analysis_results.get('overall_bias', 'Neutral')
        alignment = (flow_bias == options_bias) or (flow_bias == 'Neutral' or options_bias == 'Neutral')

        # Enhance recommendation confidence based on alignment
        for trade in best_trades + alternate_trades:
            trade_direction = trade.get('direction', 'Unknown')

            # Add money flow information to the trade
            trade['money_flow'] = {
                'bias': flow_bias,
                'strength': flow_strength,
                'alignment': alignment,
                'fii_net': money_flow_data.get('institutional_activity', {}).get('fii', {}).get('net', 0),
                'dii_net': money_flow_data.get('institutional_activity', {}).get('dii', {}).get('net', 0),
                'delivery_percentage': money_flow_data.get('delivery_percentage', None),
                'futures_oi_change': money_flow_data.get('futures_oi', {}).get('change_percent', 0)
            }

            # Adjust confidence based on money flow alignment
            original_confidence = trade.get('confidence', 50)

            if trade_direction == 'Buy' and flow_bias == 'Bullish':
                # Bullish trade with bullish money flow - increase confidence
                trade['confidence'] = min(original_confidence + 15, 100)
                trade['analysis'] += f" Money flow analysis confirms this trade with {flow_strength} bullish bias. "

                # Add specific insights
                if money_flow_data.get('institutional_activity', {}).get('fii', {}).get('net', 0) > 0:
                    trade['analysis'] += "FIIs are net buyers, supporting this bullish view. "

                if money_flow_data.get('delivery_percentage', 0) > 60:
                    trade['analysis'] += "High delivery percentage indicates genuine investing interest. "

                if money_flow_data.get('futures_oi', {}).get('change_percent', 0) > 2:
                    trade['analysis'] += "Futures OI is increasing, confirming bullish positioning. "

            elif trade_direction == 'Sell' and flow_bias == 'Bearish':
                # Bearish trade with bearish money flow - increase confidence
                trade['confidence'] = min(original_confidence + 15, 100)
                trade['analysis'] += f" Money flow analysis confirms this trade with {flow_strength} bearish bias. "

                # Add specific insights
                if money_flow_data.get('institutional_activity', {}).get('fii', {}).get('net', 0) < 0:
                    trade['analysis'] += "FIIs are net sellers, supporting this bearish view. "

                if money_flow_data.get('delivery_percentage', 0) < 40:
                    trade['analysis'] += "Low delivery percentage indicates speculative selling. "

                if money_flow_data.get('futures_oi', {}).get('change_percent', 0) < -2:
                    trade['analysis'] += "Futures OI is decreasing, confirming bearish positioning. "

            elif (trade_direction == 'Buy' and flow_bias == 'Bearish') or (trade_direction == 'Sell' and flow_bias == 'Bullish'):
                # Conflicting signals - decrease confidence
                trade['confidence'] = max(original_confidence - 10, 20)
                trade['analysis'] += f" Warning: Money flow analysis shows {flow_strength} {flow_bias.lower()} bias, which contradicts this trade. "
                trade['analysis'] += "Consider reducing position size or waiting for alignment. "
            else:
                # Neutral money flow - no significant change
                trade['analysis'] += f" Money flow analysis shows {flow_bias.lower()} bias with {flow_strength.lower()} conviction. "

        # Add overall money flow analysis to the recommendations
        recommendations['money_flow_analysis'] = money_flow_data.get('analysis', '')
        recommendations['money_flow_enhanced'] = True

        return recommendations
    except Exception as e:
        print(f"Error incorporating money flow data: {str(e)}")
        # Return original recommendations if enhancement fails
        return recommendations

@app.route('/api/money-flow', methods=['GET'])
def get_money_flow():
    """Get money flow data for a symbol"""
    try:
        symbol = request.args.get('symbol')
        if not symbol:
            return jsonify({"success": False, "message": "Symbol is required"})

        # Get Fyers client
        fyers = get_fyers_client()
        if not fyers:
            return jsonify({"success": False, "message": "Fyers API client not initialized"})

        # Fetch money flow data
        money_flow_data = fetch_money_flow_data(symbol, fyers)
        if not money_flow_data or not money_flow_data.get('success'):
            return jsonify({
                "success": False,
                "message": money_flow_data.get('message', 'Failed to fetch money flow data')
            })

        return jsonify(money_flow_data)
    except Exception as e:
        print(f"Error in money flow API: {str(e)}")
        return jsonify({"success": False, "message": f"Error: {str(e)}"})

def fetch_money_flow_data(symbol, fyers_client=None):
    """Fetch money flow data for a symbol"""
    try:
        if not fyers_client:
            fyers_client = get_fyers_client()
            if not fyers_client:
                return {
                    "success": False,
                    "message": "Failed to initialize Fyers client"
                }

        # Prepare the request data
        data = {
            "symbol": symbol,
            "resolution": "1D",
            "date_format": "1",
            "range_from": (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'),
            "range_to": datetime.now().strftime('%Y-%m-%d'),
            "cont_flag": "1"
        }

        # Make the API call
        response = fyers_client.history(data=data)
        
        if not response or response.get('s') != 'ok':
            error_message = response.get('message', 'Unknown error') if response else 'No response from API'
            return {
                "success": False,
                "message": f"Failed to fetch money flow data: {error_message}"
            }

        # Process the response
        candles = response.get('candles', [])
        if not candles:
            return {
                "success": False,
                "message": "No money flow data available"
            }

        # Calculate money flow data
        total_volume = sum(candle[5] for candle in candles)  # Volume is at index 5
        total_value = sum(candle[5] * candle[4] for candle in candles)  # Volume * Close price
        
        return {
            "success": True,
            "data": {
                "total_volume": total_volume,
                "total_value": total_value,
                "average_price": total_value / total_volume if total_volume > 0 else 0,
                "last_price": candles[-1][4] if candles else 0,  # Last close price
                "last_volume": candles[-1][5] if candles else 0  # Last volume
            }
        }

    except Exception as e:
        return {
            "success": False,
            "message": f"Error fetching money flow data: {str(e)}"
        }

@app.route('/api/institutional-analysis/volume-oi-spikes', methods=['POST'])
def analyze_volume_oi_spikes():
    """Detect volume and OI combined spikes"""
    try:
        # Get request data
        data = request.json
        options_data = data.get('options_data', [])

        if not options_data:
            return jsonify({"success": False, "message": "No options data provided"})

        # Run the analysis
        analysis_results = institutional_analyzer.detect_volume_oi_spikes(options_data)

        return jsonify(analysis_results)
    except Exception as e:
        return jsonify({"success": False, "message": f"Error analyzing volume-OI spikes: {str(e)}"})

@app.route('/api/institutional-analysis/dealer-gamma', methods=['POST'])
def analyze_dealer_gamma():
    """Analyze dealer gamma exposure"""
    try:
        # Get request data
        data = request.json
        options_data = data.get('options_data', [])
        current_price = float(data.get('current_price', 0))

        if not options_data:
            return jsonify({"success": False, "message": "No options data provided"})

        # Run the analysis
        analysis_results = institutional_analyzer.calculate_dealer_gamma(options_data, current_price)

        return jsonify(analysis_results)
    except Exception as e:
        return jsonify({"success": False, "message": f"Error analyzing dealer gamma: {str(e)}"})

@app.route('/api/institutional-analysis/iv-skew', methods=['POST'])
def analyze_iv_skew():
    """Analyze IV skew across strikes"""
    try:
        # Get request data
        data = request.json
        options_data = data.get('options_data', [])
        current_price = float(data.get('current_price', 0))

        if not options_data:
            return jsonify({"success": False, "message": "No options data provided"})

        # Run the analysis
        analysis_results = institutional_analyzer.analyze_iv_skew(options_data, current_price)

        return jsonify(analysis_results)
    except Exception as e:
        return jsonify({"success": False, "message": f"Error analyzing IV skew: {str(e)}"})

@app.route('/api/institutional-analysis/delta-weighted-oi', methods=['POST'])
def analyze_delta_weighted_oi():
    """Analyze delta-weighted open interest"""
    try:
        # Get request data
        data = request.json
        options_data = data.get('options_data', [])
        current_price = float(data.get('current_price', 0))

        if not options_data:
            return jsonify({"success": False, "message": "No options data provided"})

        # Run the analysis
        analysis_results = institutional_analyzer.calculate_delta_weighted_oi(options_data, current_price)

        return jsonify(analysis_results)
    except Exception as e:
        return jsonify({"success": False, "message": f"Error analyzing delta-weighted OI: {str(e)}"})

@app.route('/api/institutional-analysis/liquidity-depth', methods=['POST'])
def analyze_liquidity_depth():
    """Analyze liquidity depth at each strike"""
    try:
        # Get request data
        data = request.json
        options_data = data.get('options_data', [])

        if not options_data:
            return jsonify({"success": False, "message": "No options data provided"})

        # Run the analysis
        analysis_results = institutional_analyzer.analyze_liquidity_depth(options_data)

        return jsonify(analysis_results)
    except Exception as e:
        return jsonify({"success": False, "message": f"Error analyzing liquidity depth: {str(e)}"})

@app.route('/api/institutional-analysis/smart-money', methods=['POST'])
def detect_smart_money():
    """Detect smart money activity"""
    try:
        # Get request data
        data = request.json
        options_data = data.get('options_data', [])

        if not options_data:
            return jsonify({"success": False, "message": "No options data provided"})

        # Run the analysis
        analysis_results = institutional_analyzer.detect_smart_money(options_data)

        return jsonify(analysis_results)
    except Exception as e:
        return jsonify({"success": False, "message": f"Error detecting smart money: {str(e)}"})

@app.route('/api/institutional-analysis/max-pain-shift', methods=['POST'])
def track_max_pain_shift():
    """Track and analyze max pain shifts"""
    try:
        # Get request data
        data = request.json
        options_data = data.get('options_data', [])
        current_price = float(data.get('current_price', 0))

        if not options_data:
            return jsonify({"success": False, "message": "No options data provided"})

        # Use global historical max pain data
        global historical_max_pain

        # Run the analysis
        analysis_results = institutional_analyzer.track_max_pain_shift(options_data, current_price, historical_max_pain)

        # Update historical data for future use
        if analysis_results.get('success', False):
            historical_max_pain = analysis_results.get('historical_max_pain', historical_max_pain)

        return jsonify(analysis_results)
    except Exception as e:
        return jsonify({"success": False, "message": f"Error tracking max pain shift: {str(e)}"})

@app.route('/api/institutional-analysis/all', methods=['POST'])
def run_all_institutional_analysis():
    """Run all institutional-level analysis"""
    try:
        # Get request data
        data = request.json
        options_data = data.get('options_data', [])
        current_price = float(data.get('current_price', 0))

        if not options_data:
            return jsonify({"success": False, "message": "No options data provided"})

        # Use global historical max pain data
        global historical_max_pain

        # Initialize results with default values
        volume_oi_results = {"success": False, "message": "Analysis not run"}
        dealer_gamma_results = {"success": False, "message": "Analysis not run"}
        iv_skew_results = {"success": False, "message": "Analysis not run"}
        delta_weighted_oi_results = {"success": False, "message": "Analysis not run"}
        liquidity_depth_results = {"success": False, "message": "Analysis not run"}
        smart_money_results = {"success": False, "message": "Analysis not run"}
        max_pain_shift_results = {"success": False, "message": "Analysis not run"}

        # Run analyses with proper error handling
        try:
            volume_oi_results = institutional_analyzer.detect_volume_oi_spikes(options_data)
        except Exception as e:
            print(f"Error in volume-OI analysis: {str(e)}")
            volume_oi_results = {"success": False, "message": f"Error: {str(e)}"}

        try:
            dealer_gamma_results = institutional_analyzer.calculate_dealer_gamma(options_data, current_price)
        except Exception as e:
            print(f"Error in dealer gamma analysis: {str(e)}")
            dealer_gamma_results = {"success": False, "message": f"Error: {str(e)}"}

        try:
            iv_skew_results = institutional_analyzer.analyze_iv_skew(options_data, current_price)
        except Exception as e:
            print(f"Error in IV skew analysis: {str(e)}")
            iv_skew_results = {"success": False, "message": f"Error: {str(e)}"}

        try:
            delta_weighted_oi_results = institutional_analyzer.calculate_delta_weighted_oi(options_data, current_price)
        except Exception as e:
            print(f"Error in delta-weighted OI analysis: {str(e)}")
            delta_weighted_oi_results = {"success": False, "message": f"Error: {str(e)}"}

        try:
            liquidity_depth_results = institutional_analyzer.analyze_liquidity_depth(options_data)
        except Exception as e:
            print(f"Error in liquidity depth analysis: {str(e)}")
            liquidity_depth_results = {"success": False, "message": f"Error: {str(e)}"}

        try:
            smart_money_results = institutional_analyzer.detect_smart_money(options_data)
        except Exception as e:
            print(f"Error in smart money analysis: {str(e)}")
            smart_money_results = {"success": False, "message": f"Error: {str(e)}"}

        try:
            max_pain_shift_results = institutional_analyzer.track_max_pain_shift(options_data, current_price, historical_max_pain)
            # Update historical data for future use
            if max_pain_shift_results.get('success', False):
                historical_max_pain = max_pain_shift_results.get('historical_max_pain', historical_max_pain)
        except Exception as e:
            print(f"Error in max pain shift analysis: {str(e)}")
            max_pain_shift_results = {"success": False, "message": f"Error: {str(e)}"}

        # Combine all analyses
        combined_analysis = ""
        if volume_oi_results.get('success', False):
            combined_analysis += "VOLUME-OI ANALYSIS: " + volume_oi_results.get('summary', '') + "\n\n"
        if dealer_gamma_results.get('success', False):
            combined_analysis += "DEALER GAMMA ANALYSIS: " + dealer_gamma_results.get('analysis', '') + "\n\n"
        if iv_skew_results.get('success', False):
            combined_analysis += "IV SKEW ANALYSIS: " + iv_skew_results.get('analysis', '') + "\n\n"
        if delta_weighted_oi_results.get('success', False):
            combined_analysis += "DELTA-WEIGHTED OI ANALYSIS: " + delta_weighted_oi_results.get('analysis', '') + "\n\n"
        if liquidity_depth_results.get('success', False):
            combined_analysis += "LIQUIDITY DEPTH ANALYSIS: " + liquidity_depth_results.get('analysis', '') + "\n\n"
        if smart_money_results.get('success', False):
            combined_analysis += "SMART MONEY ANALYSIS: " + smart_money_results.get('analysis', '') + "\n\n"
        if max_pain_shift_results.get('success', False):
            combined_analysis += "MAX PAIN SHIFT ANALYSIS: " + max_pain_shift_results.get('analysis', '') + "\n\n"

        return jsonify({
            "success": True,
            "volume_oi_spikes": volume_oi_results if volume_oi_results.get('success', False) else None,
            "dealer_gamma": dealer_gamma_results if dealer_gamma_results.get('success', False) else None,
            "iv_skew": iv_skew_results if iv_skew_results.get('success', False) else None,
            "delta_weighted_oi": delta_weighted_oi_results if delta_weighted_oi_results.get('success', False) else None,
            "liquidity_depth": liquidity_depth_results if liquidity_depth_results.get('success', False) else None,
            "smart_money": smart_money_results if smart_money_results.get('success', False) else None,
            "max_pain_shift": max_pain_shift_results if max_pain_shift_results.get('success', False) else None,
            "combined_analysis": combined_analysis
        })
    except Exception as e:
        print(f"Error running institutional analysis: {str(e)}")
        return jsonify({"success": False, "message": f"Error running institutional analysis: {str(e)}"})

# Batch Analysis API Endpoints
@app.route('/api/watchlists', methods=['GET'])
def get_watchlists():
    """Get all available watchlists"""
    try:
        watchlists = watchlist_manager.get_all_watchlists()
        return jsonify({
            "success": True,
            "watchlists": watchlists
        })
    except Exception as e:
        return jsonify({"success": False, "message": f"Error getting watchlists: {str(e)}"})

@app.route('/api/watchlists/<name>', methods=['GET'])
def get_watchlist(name):
    """Get a specific watchlist by name"""
    try:
        watchlist = watchlist_manager.get_watchlist(name)
        if watchlist:
            return jsonify({
                "success": True,
                "name": name,
                "symbols": watchlist
            })
        else:
            return jsonify({"success": False, "message": f"Watchlist '{name}' not found"})
    except Exception as e:
        return jsonify({"success": False, "message": f"Error getting watchlist: {str(e)}"})

@app.route('/api/watchlists', methods=['POST'])
def create_watchlist():
    """Create a new watchlist"""
    try:
        data = request.json
        name = data.get('name')
        symbols = data.get('symbols', [])

        if not name:
            return jsonify({"success": False, "message": "Watchlist name is required"})

        result = watchlist_manager.create_watchlist(name, symbols)
        if result:
            return jsonify({
                "success": True,
                "message": f"Watchlist '{name}' created successfully"
            })
        else:
            return jsonify({"success": False, "message": f"Watchlist '{name}' already exists"})
    except Exception as e:
        return jsonify({"success": False, "message": f"Error creating watchlist: {str(e)}"})

@app.route('/api/watchlists/<name>', methods=['PUT'])
def update_watchlist(name):
    """Update a watchlist"""
    try:
        data = request.json
        action = data.get('action')
        symbol = data.get('symbol')

        if not action or not symbol:
            return jsonify({"success": False, "message": "Action and symbol are required"})

        if action == 'add':
            result = watchlist_manager.add_to_watchlist(name, symbol)
            if result:
                return jsonify({
                    "success": True,
                    "message": f"Symbol '{symbol}' added to watchlist '{name}'"
                })
            else:
                return jsonify({"success": False, "message": f"Symbol '{symbol}' already in watchlist '{name}'"})
        elif action == 'remove':
            result = watchlist_manager.remove_from_watchlist(name, symbol)
            if result:
                return jsonify({
                    "success": True,
                    "message": f"Symbol '{symbol}' removed from watchlist '{name}'"
                })
            else:
                return jsonify({"success": False, "message": f"Symbol '{symbol}' not found in watchlist '{name}'"})
        else:
            return jsonify({"success": False, "message": f"Invalid action: {action}"})
    except Exception as e:
        return jsonify({"success": False, "message": f"Error updating watchlist: {str(e)}"})

@app.route('/api/watchlists/<name>', methods=['DELETE'])
def delete_watchlist(name):
    """Delete a watchlist"""
    try:
        result = watchlist_manager.delete_watchlist(name)
        if result:
            return jsonify({
                "success": True,
                "message": f"Watchlist '{name}' deleted successfully"
            })
        else:
            return jsonify({"success": False, "message": f"Watchlist '{name}' not found"})
    except Exception as e:
        return jsonify({"success": False, "message": f"Error deleting watchlist: {str(e)}"})

@app.route('/api/batch-analysis/start', methods=['POST'])
def start_batch_analysis():
    """Start batch analysis of a watchlist"""
    try:
        data = request.json
        watchlist_name = data.get('watchlist')
        expiry_date = data.get('expiry_date')
        settings = data.get('settings')

        if not watchlist_name:
            return jsonify({"success": False, "message": "Watchlist name is required"})

        # Generate a unique task ID
        task_id = str(uuid.uuid4())

        # Store task ID in session
        session['current_analysis_task'] = task_id

        # Create a new batch analyzer instance for this task
        task_analyzer = BatchAnalyzer()

        # Update batch analyzer settings if provided
        if settings:
            task_analyzer.update_settings(settings)

        # Store the analyzer in the background tasks dictionary
        background_tasks[task_id] = {
            'analyzer': task_analyzer,
            'status': 'starting',
            'watchlist': watchlist_name,
            'expiry_date': expiry_date,
            'start_time': datetime.now(),
            'results': []
        }

        # Start the analysis
        success, message = task_analyzer.start_analysis(watchlist_name, expiry_date)

        if success:
            background_tasks[task_id]['status'] = 'running'
        else:
            background_tasks[task_id]['status'] = 'failed'
            background_tasks[task_id]['error'] = message

        return jsonify({
            "success": success,
            "message": message,
            "task_id": task_id
        })
    except Exception as e:
        return jsonify({"success": False, "message": f"Error starting batch analysis: {str(e)}"})

@app.route('/api/batch-analysis/progress', methods=['GET'])
def get_batch_analysis_progress():
    """Get the current progress of batch analysis"""
    try:
        # Get task ID from query parameter or session
        task_id = request.args.get('task_id')
        if not task_id and 'current_analysis_task' in session:
            task_id = session.get('current_analysis_task')

        if not task_id:
            return jsonify({"success": False, "message": "No active analysis task found"})

        # Check if task exists
        if task_id not in background_tasks:
            return jsonify({"success": False, "message": "Analysis task not found"})

        # Get the task
        task = background_tasks[task_id]
        task_analyzer = task.get('analyzer')

        if not task_analyzer:
            return jsonify({"success": False, "message": "Analyzer not found for this task"})

        # Get progress
        progress = task_analyzer.get_progress()

        # Update task status if complete
        if progress['progress'] >= 100:
            task['status'] = 'completed'
            task['results'] = task_analyzer.get_top_trades(10)  # Store top 10 results

        # Add task info to progress
        progress['task_id'] = task_id
        progress['status'] = task['status']
        progress['watchlist'] = task['watchlist']
        progress['start_time'] = task['start_time'].strftime('%Y-%m-%d %H:%M:%S')
        progress['elapsed_time'] = str(datetime.now() - task['start_time']).split('.')[0]  # Format as HH:MM:SS

        return jsonify({
            "success": True,
            "progress": progress
        })
    except Exception as e:
        return jsonify({"success": False, "message": f"Error getting batch analysis progress: {str(e)}"})

@app.route('/api/batch-analysis/results', methods=['GET'])
def get_batch_analysis_results():
    """Get the results of batch analysis"""
    try:
        # Get task ID from query parameter or session
        task_id = request.args.get('task_id')
        if not task_id and 'current_analysis_task' in session:
            task_id = session.get('current_analysis_task')

        count = request.args.get('count', 3, type=int)

        if not task_id:
            return jsonify({"success": False, "message": "No active analysis task found"})

        # Check if task exists
        if task_id not in background_tasks:
            return jsonify({"success": False, "message": "Analysis task not found"})

        # Get the task
        task = background_tasks[task_id]
        task_analyzer = task.get('analyzer')

        # If task is completed, use stored results
        if task['status'] == 'completed' and task.get('results'):
            results = task['results'][:count]
        elif task_analyzer:
            # Otherwise get results from analyzer
            results = task_analyzer.get_top_trades(count)
            # Store results if not already stored
            if not task.get('results'):
                task['results'] = task_analyzer.get_top_trades(10)  # Store top 10 results
        else:
            return jsonify({"success": False, "message": "No results available for this task"})

        return jsonify({
            "success": True,
            "results": results,
            "count": len(results),
            "task_id": task_id,
            "status": task['status']
        })
    except Exception as e:
        return jsonify({"success": False, "message": f"Error getting batch analysis results: {str(e)}"})

@app.route('/api/batch-analysis/cancel', methods=['POST'])
def cancel_batch_analysis():
    """Cancel the current batch analysis"""
    try:
        # Get task ID from request body or session
        data = request.json or {}
        task_id = data.get('task_id')
        if not task_id and 'current_analysis_task' in session:
            task_id = session.get('current_analysis_task')

        if not task_id:
            return jsonify({"success": False, "message": "No active analysis task found"})

        # Check if task exists
        if task_id not in background_tasks:
            return jsonify({"success": False, "message": "Analysis task not found"})

        # Get the task
        task = background_tasks[task_id]
        task_analyzer = task.get('analyzer')

        if not task_analyzer:
            return jsonify({"success": False, "message": "Analyzer not found for this task"})

        # Cancel the analysis
        result = task_analyzer.cancel_analysis()

        if result:
            task['status'] = 'cancelled'

        return jsonify({
            "success": result,
            "message": "Batch analysis cancelled successfully" if result else "Failed to cancel batch analysis",
            "task_id": task_id
        })
    except Exception as e:
        return jsonify({"success": False, "message": f"Error cancelling batch analysis: {str(e)}"})

@app.route('/api/batch-analysis/tasks', methods=['GET'])
def get_batch_analysis_tasks():
    """Get all batch analysis tasks"""
    try:
        # Convert tasks to a serializable format
        tasks_list = []
        for task_id, task in background_tasks.items():
            task_info = {
                'task_id': task_id,
                'status': task['status'],
                'watchlist': task['watchlist'],
                'expiry_date': task['expiry_date'],
                'start_time': task['start_time'].strftime('%Y-%m-%d %H:%M:%S'),
                'elapsed_time': str(datetime.now() - task['start_time']).split('.')[0]
            }

            # Add progress info if available
            if 'analyzer' in task:
                try:
                    progress = task['analyzer'].get_progress()
                    task_info['progress'] = progress['progress']
                    task_info['analyzed'] = progress['analyzed']
                    task_info['total_stocks'] = progress['total_stocks']
                except:
                    task_info['progress'] = 0

            # Add result count if available
            if 'results' in task:
                task_info['result_count'] = len(task['results'])

            tasks_list.append(task_info)

        # Sort by start time (newest first)
        tasks_list.sort(key=lambda x: x['start_time'], reverse=True)

        return jsonify({
            "success": True,
            "tasks": tasks_list,
            "count": len(tasks_list),
            "current_task": session.get('current_analysis_task')
        })
    except Exception as e:
        return jsonify({"success": False, "message": f"Error getting batch analysis tasks: {str(e)}"})

@app.route('/analyze-all', methods=['GET'])
def analyze_all_page():
    """Render the analyze all page"""
    return render_template('analyze_all.html')

if __name__ == '__main__':
    app.run(debug=True)
