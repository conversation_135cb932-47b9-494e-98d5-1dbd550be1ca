#!/usr/bin/env python3
"""
Test script to verify Fyers API connection and AI integration
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_fyers_connection():
    """Test Fyers API connection"""
    print("🔄 Testing Fyers API Connection...")
    
    try:
        from app import get_fyers_client
        
        # Get Fyers client
        fyers = get_fyers_client()
        if not fyers:
            print("❌ Failed to initialize Fyers client")
            return False
        
        print("✅ Fyers client initialized successfully")
        
        # Test API call
        print("🔄 Testing option chain API call...")
        data = {
            "symbol": "NSE:NIFTY50-INDEX",
            "strikecount": 5,
            "timestamp": ""
        }
        
        response = fyers.optionchain(data=data)
        
        if response and response.get('s') == 'ok':
            print("✅ Option chain API call successful")
            print(f"📊 Retrieved {len(response.get('data', {}).get('optionsChain', []))} options")
            return True
        else:
            print(f"❌ Option chain API call failed: {response}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Fyers connection: {str(e)}")
        return False

def test_ai_integration():
    """Test AI integration"""
    print("\n🔄 Testing AI Integration...")
    
    try:
        from gemini_ai_client import GeminiOptionsAnalyzer
        
        # Initialize AI analyzer
        ai_analyzer = GeminiOptionsAnalyzer()
        print("✅ Gemini AI analyzer initialized successfully")
        
        # Test with sample data
        sample_options = [
            {
                'Strike Price': 24000,
                'Option Type': 'CE',
                'OI': 1000,
                'Volume': 500,
                'LTP': 150.5,
                'Change': 10.2
            },
            {
                'Strike Price': 24000,
                'Option Type': 'PE',
                'OI': 1200,
                'Volume': 600,
                'LTP': 120.3,
                'Change': -5.1
            }
        ]
        
        print("🔄 Testing AI analysis...")
        result = ai_analyzer.analyze_option_chain(
            options_data=sample_options,
            current_price=24050,
            symbol="NSE:NIFTY50-INDEX"
        )
        
        if result.get('success'):
            print("✅ AI analysis completed successfully")
            return True
        else:
            print(f"❌ AI analysis failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing AI integration: {str(e)}")
        return False

def test_environment_variables():
    """Test environment variables"""
    print("\n🔄 Testing Environment Variables...")
    
    required_vars = [
        'FYERS_APP_ID',
        'FYERS_SECRET_ID', 
        'FYERS_CLIENT_ID',
        'FYERS_ACCESS_TOKEN',
        'GEMINI_API_KEY'
    ]
    
    all_present = True
    for var in required_vars:
        value = os.environ.get(var)
        if value:
            print(f"✅ {var}: {'*' * (len(value) - 10) + value[-10:] if len(value) > 10 else '***'}")
        else:
            print(f"❌ {var}: Not found")
            all_present = False
    
    return all_present

def main():
    """Main test function"""
    print("🚀 Fyers Options Analysis - System Test")
    print("=" * 50)
    
    # Test environment variables
    env_test = test_environment_variables()
    
    # Test Fyers connection
    fyers_test = test_fyers_connection()
    
    # Test AI integration
    ai_test = test_ai_integration()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY:")
    print(f"Environment Variables: {'✅ PASS' if env_test else '❌ FAIL'}")
    print(f"Fyers API Connection: {'✅ PASS' if fyers_test else '❌ FAIL'}")
    print(f"AI Integration: {'✅ PASS' if ai_test else '❌ FAIL'}")
    
    if all([env_test, fyers_test, ai_test]):
        print("\n🎉 ALL TESTS PASSED! Your system is ready for AI-powered options analysis.")
        print("\n🌐 Access your application at: http://127.0.0.1:5000")
        print("🤖 AI Analysis page: http://127.0.0.1:5000/ai-options-analysis")
    else:
        print("\n⚠️  Some tests failed. Please check the configuration.")
        
    return all([env_test, fyers_test, ai_test])

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
