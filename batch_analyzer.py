"""
Batch analyzer for processing multiple stocks and finding the best trade opportunities
"""

import time
import threading
import os
import json
from watchlist_manager import WatchlistManager
from options_analysis import OptionsAnalysis
from enhanced_scoring import EnhancedScoring
from fyers_apiv3 import fyersModel
from datetime import datetime
import pandas as pd
class BatchAnalyzer:
    def __init__(self, fyers_client=None):
        """Initialize the batch analyzer"""
        self.watchlist_manager = WatchlistManager()
        self.options_analyzer = OptionsAnalysis()
        self.enhanced_scorer = EnhancedScoring()
        self.fyers_client = fyers_client

        # Analysis settings
        self.settings = {
            'min_volume': 1000,     # Minimum volume for liquidity filter (reduced from 500000)
            'min_volatility': 0.1,  # Minimum price change % for volatility filter (reduced from 1.0)
            'weights': {
                'money_flow': 0.30,  # 30% weight for money flow
                'oi_analysis': 0.30,  # 30% weight for OI analysis
                'trend': 0.20,       # 20% weight for technical trend
                'pcr': 0.10,         # 10% weight for PCR
                'news_risk': 0.10    # 10% weight for news event risk
            }
        }

        # Results storage
        self.analysis_results = []
        self.progress = 0
        self.total_stocks = 0
        self.is_analyzing = False
        self.analysis_thread = None
        self.progress_callback = None
        self.completion_callback = None

    def set_progress_callback(self, callback):
        """Set callback function for progress updates"""
        self.progress_callback = callback

    def set_completion_callback(self, callback):
        """Set callback function for analysis completion"""
        self.completion_callback = callback

    def update_settings(self, settings):
        """Update analysis settings"""
        if settings:
            self.settings.update(settings)

    def start_analysis(self, watchlist_name, expiry_date=None):
        """Start batch analysis of a watchlist"""
        if self.is_analyzing:
            return False, "Analysis already in progress"

        # Get the watchlist
        watchlist = self.watchlist_manager.get_watchlist(watchlist_name)
        if not watchlist:
            return False, f"Watchlist '{watchlist_name}' not found or empty"

        # Reset progress and results
        self.analysis_results = []
        self.progress = 0
        self.total_stocks = len(watchlist)
        self.is_analyzing = True

        # Start analysis in a separate thread
        self.analysis_thread = threading.Thread(
            target=self._analyze_watchlist,
            args=(watchlist, expiry_date)
        )
        self.analysis_thread.daemon = True
        self.analysis_thread.start()

        return True, f"Started analysis of {self.total_stocks} stocks"

    def _analyze_watchlist(self, watchlist, expiry_date):
        """Analyze all stocks in a watchlist (runs in a separate thread)"""
        try:
            self.is_analyzing = True
            self.analysis_results = []

            for symbol in watchlist:
                result = self._analyze_stock(symbol, expiry_date)
                if result:
                    self.analysis_results.append(result)

            # If no results were found, print a clear error message
            if not self.analysis_results:
                print("No valid results found. This application requires real-time data from Fyers API.")
                print("Please check your Fyers API token, ensure it's valid, and try again.")
                print("Note: This application does not use mock or dummy data - real data is required for analysis.")
        except Exception as e:
            print(f"Error in batch analysis: {str(e)}")
        finally:
            self.is_analyzing = False

    def _analyze_stock(self, symbol, expiry_date):
        """Analyze a single stock and find the best trade"""
        try:
            print(f"Starting analysis for {symbol} with expiry date {expiry_date}")

            # Fetch option chain data using Fyers API
            options_data, current_price, is_mock_data = self._fetch_option_chain(symbol, expiry_date)
            if not options_data or not current_price:
                print(f"No option chain data found for {symbol}")
                return None

            # Ensure we're using real data, not mock data
            if is_mock_data:
                print(f"Mock data detected for {symbol}. This application requires real data from Fyers API.")
                return None

            print(f"Fetched option chain data for {symbol}: {len(options_data)} options, current price: {current_price}")

            # Fetch money flow data
            money_flow_data, is_real_data = self._fetch_money_flow_data(symbol)
            if money_flow_data and is_real_data:
                print(f"Fetched real money flow data for {symbol}: {money_flow_data.get('flow_bias', 'Unknown')} bias")
            elif money_flow_data:
                print(f"Warning: Money flow data for {symbol} may not be complete")
            else:
                print(f"No money flow data available for {symbol}. Real-time data is required.")
                return None

            # Analyze the data
            analysis = self._analyze_options_data(options_data, current_price, money_flow_data)
            if analysis:
                analysis['symbol'] = symbol
                analysis['current_price'] = current_price
                return analysis

            return None
        except Exception as e:
            print(f"Error analyzing {symbol}: {str(e)}")
            return None

    def _fetch_option_chain(self, symbol, expiry_date):
        """Fetch option chain data for a symbol using Fyers API"""
        try:
            if not self.fyers_client:
                print("Fyers API client not initialized")
                return None, None, False

            # Fetch option chain data
            data = {
                "symbol": symbol,
                "strikecount": 5,  # Default to 5 strikes
                "timestamp": ""
            }
            response = self.fyers_client.optionchain(data=data)
            
            if not response or response.get('s') != 'ok':
                print(f"Error fetching option chain for {symbol}")
                return None, None, False

            # Process the response
            api_data = response['data']
            options_chain = api_data.get('optionsChain', [])
            
            if not options_chain:
                print(f"No options data available for {symbol}")
                return None, None, False

            # Extract the underlying value
            underlying_value = 0
            for item in options_chain:
                if 'strike_price' in item and item['strike_price'] == -1:
                    underlying_value = item.get('ltp', 0)
                    break

            if not underlying_value:
                print(f"No underlying value available for {symbol}")
                return None, None, False

            # Process options data
            processed_options = []
            has_real_oi_data = False

            for item in options_chain:
                # Skip the underlying symbol
                if 'strike_price' in item and item['strike_price'] == -1:
                    continue

                strike_price = item.get('strike_price', 0)
                option_type = item.get('option_type', '')

                if not strike_price or not option_type:
                    continue

                # Check if we have real OI data
                if item.get('oi', 0) > 0:
                    has_real_oi_data = True

                # Find or create an option entry for this strike price
                option_entry = next((opt for opt in processed_options if opt['strike_price'] == strike_price), None)

                if not option_entry:
                    option_entry = {
                        'strike_price': strike_price,
                        'is_atm': abs(strike_price - underlying_value) < 0.01,
                        'call': {},
                        'put': {}
                    }
                    processed_options.append(option_entry)

                # Add data for this option type (call or put)
                if option_type == 'CE':
                    option_entry['call'] = {
                        'open_interest': item.get('oi', 0),
                        'change_in_oi': item.get('oich', 0),
                        'volume': item.get('volume', 0),
                        'last_price': item.get('ltp', 0),
                        'change': item.get('ltpch', 0),
                        'bid_price': item.get('bid', 0),
                        'ask_price': item.get('ask', 0)
                    }
                elif option_type == 'PE':
                    option_entry['put'] = {
                        'open_interest': item.get('oi', 0),
                        'change_in_oi': item.get('oich', 0),
                        'volume': item.get('volume', 0),
                        'last_price': item.get('ltp', 0),
                        'change': item.get('ltpch', 0),
                        'bid_price': item.get('bid', 0),
                        'ask_price': item.get('ask', 0)
                    }

            # Sort options by strike price
            processed_options.sort(key=lambda x: x['strike_price'])

            return processed_options, underlying_value, has_real_oi_data

        except Exception as e:
            print(f"Error in _fetch_option_chain: {str(e)}")
            return None, None, False

    def _fetch_money_flow_data(self, symbol):
        """Fetch money flow data for a symbol"""
        try:
            if not self.fyers_client:
                print("Fyers API client not initialized")
                return None, False

            # Fetch historical data for money flow calculation
            historical_data = self.fyers_client.get_historical_data(symbol)
            if not historical_data:
                print(f"No historical data available for {symbol}")
                return None, False

            # Calculate money flow indicators
            money_flow = self._calculate_money_flow(historical_data)
            if not money_flow:
                print(f"Could not calculate money flow for {symbol}")
                return None, False

            return money_flow, True  # Real data
        except Exception as e:
            print(f"Error fetching money flow data for {symbol}: {str(e)}")
            return None, False

    def _passes_liquidity_filter(self, options_data):
        """Check if the options data passes the liquidity filter"""
        # Set minimum volume requirement
        min_volume = self.settings.get('min_volume', 1000)  # Minimum volume requirement

        # Check if at least one option has sufficient volume
        for option in options_data:
            volume = option.get('Volume', 0)
            oi = option.get('OI', 0)

            # Pass if either volume or OI is sufficient
            if volume >= min_volume or oi >= min_volume * 2:
                return True

        # If no options pass the filter, fail the check
        print("Failed liquidity filter: No options with sufficient volume or OI")
        return False

    def _passes_volatility_filter(self, options_data, current_price):
        """Check if the options data passes the volatility filter"""
        # Set minimum volatility requirement
        min_volatility = self.settings.get('min_volatility', 0.1)  # Minimum volatility requirement

        # Check if at least one option has sufficient price change or IV
        for option in options_data:
            change = abs(option.get('Change', 0))
            # Use current_price to calculate relative change if needed
            relative_change = (change / current_price) * 100 if current_price > 0 else 0

            # Also check IV as an alternative volatility measure
            iv = option.get('IV', 0)

            # Pass if any volatility measure is sufficient
            if change >= min_volatility or relative_change >= min_volatility or iv >= 15:
                return True

        # If no options pass the filter, fail the check
        print("Failed volatility filter: No options with sufficient price change or IV")
        return False

    def _has_conflicting_signals(self, trade, money_flow_data, analysis_results):
        """Check if there are conflicting signals between different analysis factors"""
        # Get the trade direction
        trade_direction = trade.get('option_type', '')
        is_bullish_trade = trade_direction == 'CALL'

        # Get money flow bias
        money_flow_bias = money_flow_data.get('flow_bias', 'Neutral') if money_flow_data else 'Neutral'
        is_bullish_money_flow = money_flow_bias == 'Bullish'
        is_bearish_money_flow = money_flow_bias == 'Bearish'

        # Get options analysis bias
        options_bias = analysis_results.get('overall_bias', 'Neutral')
        is_bullish_options = options_bias == 'Bullish'
        is_bearish_options = options_bias == 'Bearish'

        # Check for strong conflicts
        strong_conflict = (
            (is_bullish_trade and is_bearish_money_flow and is_bearish_options) or
            (not is_bullish_trade and is_bullish_money_flow and is_bullish_options)
        )

        return strong_conflict

    def get_top_trades(self, count=3):
        """Get the top N trades from the analysis results"""
        return self.analysis_results[:count]

    def get_progress(self):
        """Get the current analysis progress"""
        try:
            # Ensure proper type conversion
            progress = float(self.progress)
            total_stocks = int(self.total_stocks) if self.total_stocks else 0

            # Calculate analyzed stocks safely
            if total_stocks > 0 and progress > 0:
                analyzed = int(total_stocks * (progress / 100))
            else:
                analyzed = 0

            # Get the current message
            message = ""
            if progress >= 100:
                if not self.analysis_results:
                    message = "Data fetched successfully, but no stocks passed our analysis criteria."
                elif len(self.analysis_results) < total_stocks / 2:
                    message = f"Analysis complete. Found {len(self.analysis_results)} trade opportunities out of {total_stocks} stocks."
                else:
                    message = f"Analysis complete. Found {len(self.analysis_results)} trade opportunities."

            return {
                'progress': progress,
                'total_stocks': total_stocks,
                'analyzed': analyzed,
                'is_analyzing': bool(self.is_analyzing),
                'message': message
            }
        except (ValueError, TypeError):
            # Fallback in case of any conversion errors
            return {
                'progress': 0,
                'total_stocks': 0,
                'analyzed': 0,
                'is_analyzing': False,
                'message': ""
            }

    def cancel_analysis(self):
        """Cancel the current analysis"""
        self.is_analyzing = False
        return True
