body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.card {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 10px;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
}

.selector-row {
    transition: all 0.3s ease;
}

.selector-row:hover {
    background-color: #f8f9fa;
    border-radius: 5px;
}

.remove-selector {
    padding: 0.25rem 0.5rem;
    font-size: 1.2rem;
    line-height: 1;
}

#loadingIndicator {
    padding: 20px;
}

.table {
    font-size: 0.9rem;
}

.table th {
    background-color: #f1f1f1;
}

#dataPreview {
    max-height: 400px;
    overflow-y: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .selector-row .col-md-1 {
        margin-top: 10px;
    }
}
