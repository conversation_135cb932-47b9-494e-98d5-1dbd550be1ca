#!/usr/bin/env python3
"""
Token validation script for Fyers API
"""

import os
import json
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def validate_token_format():
    """Validate token format and expiry"""
    print("🔄 Validating Fyers Token...")
    
    # Get token from environment
    token = os.environ.get('FYERS_ACCESS_TOKEN')
    if not token:
        print("❌ No token found in environment variables")
        return False
    
    print(f"✅ Token found: {token[:50]}...")
    
    # Check if token is JWT format
    if token.count('.') == 2:
        print("✅ Token appears to be in JWT format")
        
        # Try to decode the payload (without verification)
        try:
            import base64
            import json
            
            # Split the token
            header, payload, signature = token.split('.')
            
            # Add padding if needed
            payload += '=' * (4 - len(payload) % 4)
            
            # Decode payload
            decoded_payload = base64.b64decode(payload)
            payload_data = json.loads(decoded_payload)
            
            print("✅ Token payload decoded successfully")
            
            # Check expiry
            if 'exp' in payload_data:
                exp_timestamp = payload_data['exp']
                exp_date = datetime.fromtimestamp(exp_timestamp)
                current_date = datetime.now()
                
                print(f"📅 Token expires: {exp_date}")
                print(f"📅 Current time: {current_date}")
                
                if exp_date > current_date:
                    print("✅ Token is not expired")
                    return True
                else:
                    print("❌ Token has expired")
                    return False
            else:
                print("⚠️  No expiry information found in token")
                return True
                
        except Exception as e:
            print(f"❌ Error decoding token: {str(e)}")
            return False
    else:
        print("❌ Token is not in JWT format")
        return False

def test_fyers_profile():
    """Test Fyers API with profile endpoint"""
    print("\n🔄 Testing Fyers API Profile...")
    
    try:
        from fyers_apiv3 import fyersModel
        
        client_id = os.environ.get('FYERS_CLIENT_ID')
        token = os.environ.get('FYERS_ACCESS_TOKEN')
        
        if not client_id or not token:
            print("❌ Missing client ID or token")
            return False
        
        # Create Fyers client
        fyers = fyersModel.FyersModel(
            client_id=client_id,
            token=token,
            is_async=False,
            log_path=""
        )
        
        # Test profile endpoint (simpler than option chain)
        print("🔄 Calling profile API...")
        response = fyers.get_profile()
        
        if response and response.get('s') == 'ok':
            print("✅ Profile API call successful")
            profile_data = response.get('data', {})
            print(f"📊 User ID: {profile_data.get('fy_id', 'Unknown')}")
            print(f"📊 Display Name: {profile_data.get('display_name', 'Unknown')}")
            return True
        else:
            print(f"❌ Profile API call failed: {response}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing profile API: {str(e)}")
        return False

def generate_new_token_instructions():
    """Provide instructions for generating a new token"""
    print("\n📋 HOW TO GENERATE A NEW FYERS TOKEN:")
    print("=" * 50)
    print("1. Go to https://myapi.fyers.in/")
    print("2. Login with your Fyers credentials")
    print("3. Go to 'My Apps' section")
    print("4. Find your app: B5QJCSZE9E-100")
    print("5. Click 'Generate Token'")
    print("6. Complete the authentication process")
    print("7. Copy the new access token")
    print("8. Update the .env file with the new token")
    print("9. Restart the application")
    print("\nNote: Tokens typically expire after 24 hours and need to be regenerated.")

def main():
    """Main validation function"""
    print("🔐 Fyers Token Validation")
    print("=" * 40)
    
    # Validate token format
    format_valid = validate_token_format()
    
    # Test API call
    api_valid = test_fyers_profile()
    
    # Summary
    print("\n" + "=" * 40)
    print("📋 VALIDATION SUMMARY:")
    print(f"Token Format: {'✅ VALID' if format_valid else '❌ INVALID'}")
    print(f"API Access: {'✅ WORKING' if api_valid else '❌ FAILED'}")
    
    if not api_valid:
        generate_new_token_instructions()
    else:
        print("\n🎉 Token is valid and working!")
        
    return api_valid

if __name__ == "__main__":
    main()
