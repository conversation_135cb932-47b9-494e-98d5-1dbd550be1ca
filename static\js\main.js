document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('crawlerForm');
    const addSelectorBtn = document.getElementById('addSelector');
    const selectorsContainer = document.getElementById('selectorsContainer');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const resultsContainer = document.getElementById('resultsContainer');
    const resultMessage = document.getElementById('resultMessage');
    const dataPreview = document.getElementById('dataPreview');
    const structuresPreview = document.getElementById('structuresPreview');
    const structuresAccordion = document.getElementById('structuresAccordion');
    const previewTableHead = document.getElementById('previewTableHead');
    const previewTableBody = document.getElementById('previewTableBody');
    const downloadLink = document.getElementById('downloadLink');
    const autoDetectSwitch = document.getElementById('autoDetectSwitch');
    const autoDetectInput = document.getElementById('autoDetectInput');
    const manualSelectors = document.getElementById('manualSelectors');

    // Toggle between automatic and manual modes
    autoDetectSwitch.addEventListener('change', function() {
        if (this.checked) {
            // Auto-detect mode
            manualSelectors.classList.add('d-none');
            autoDetectInput.value = 'true';
            // Make selector inputs not required
            document.querySelectorAll('.selector-input').forEach(input => {
                input.removeAttribute('required');
            });
        } else {
            // Manual mode
            manualSelectors.classList.remove('d-none');
            autoDetectInput.value = 'false';
            // Make selector inputs required
            document.querySelectorAll('.selector-input').forEach(input => {
                input.setAttribute('required', '');
            });
        }
    });

    // Add a new selector row
    addSelectorBtn.addEventListener('click', function() {
        const newRow = document.createElement('div');
        newRow.className = 'row selector-row mb-2';
        newRow.innerHTML = `
            <div class="col-md-5">
                <input type="text" class="form-control selector-input" name="header[]" placeholder="CSV Header (e.g., Title)" ${!autoDetectSwitch.checked ? 'required' : ''}>
            </div>
            <div class="col-md-6">
                <input type="text" class="form-control selector-input" name="selector[]" placeholder="CSS Selector (e.g., h1.title)" ${!autoDetectSwitch.checked ? 'required' : ''}>
            </div>
            <div class="col-md-1">
                <button type="button" class="btn btn-danger remove-selector">×</button>
            </div>
        `;
        selectorsContainer.appendChild(newRow);
    });

    // Remove a selector row
    selectorsContainer.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-selector')) {
            const row = e.target.closest('.selector-row');
            if (selectorsContainer.children.length > 1) {
                row.remove();
            } else {
                alert('You need at least one selector field.');
            }
        }
    });

    // Create a table preview for a data structure
    function createTablePreview(data, headers) {
        let tableHTML = '<div class="table-responsive"><table class="table table-striped table-bordered table-sm">';

        // Create header row
        tableHTML += '<thead><tr>';
        headers.forEach(header => {
            tableHTML += `<th>${header}</th>`;
        });
        tableHTML += '</tr></thead>';

        // Create body rows
        tableHTML += '<tbody>';
        data.forEach(item => {
            tableHTML += '<tr>';
            headers.forEach(header => {
                tableHTML += `<td>${item[header] || ''}</td>`;
            });
            tableHTML += '</tr>';
        });
        tableHTML += '</tbody></table></div>';

        return tableHTML;
    }

    // Handle form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Show loading indicator
        loadingIndicator.classList.remove('d-none');
        resultsContainer.classList.add('d-none');
        dataPreview.classList.add('d-none');
        structuresPreview.classList.add('d-none');

        // Collect form data
        const formData = new FormData(form);

        // Send AJAX request
        fetch('/crawl', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // Hide loading indicator
            loadingIndicator.classList.add('d-none');
            resultsContainer.classList.remove('d-none');

            if (data.success) {
                // Show success message
                resultMessage.classList.remove('alert-danger');
                resultMessage.classList.add('alert-success');
                resultMessage.textContent = data.message;

                if (data.structures) {
                    // Auto-detect mode with multiple structures
                    structuresPreview.classList.remove('d-none');
                    structuresAccordion.innerHTML = ''; // Clear previous results

                    // Create accordion items for each structure
                    data.structures.forEach((structure, index) => {
                        const structureId = `structure-${index}`;
                        const headers = structure.data.length > 0 ? Object.keys(structure.data[0]) : [];

                        const accordionItem = document.createElement('div');
                        accordionItem.className = 'accordion-item';
                        accordionItem.innerHTML = `
                            <h2 class="accordion-header" id="heading-${structureId}">
                                <button class="accordion-button ${index === 0 ? '' : 'collapsed'}" type="button"
                                        data-bs-toggle="collapse" data-bs-target="#collapse-${structureId}"
                                        aria-expanded="${index === 0 ? 'true' : 'false'}" aria-controls="collapse-${structureId}">
                                    ${structure.name} (${structure.count} items)
                                </button>
                            </h2>
                            <div id="collapse-${structureId}" class="accordion-collapse collapse ${index === 0 ? 'show' : ''}"
                                 aria-labelledby="heading-${structureId}" data-bs-parent="#structuresAccordion">
                                <div class="accordion-body">
                                    ${createTablePreview(structure.data, headers)}
                                    <div class="mt-3">
                                        <a href="/download/${structure.file_path}" class="btn btn-success btn-sm" download="${structure.file_path}">
                                            Download CSV
                                        </a>
                                    </div>
                                </div>
                            </div>
                        `;

                        structuresAccordion.appendChild(accordionItem);
                    });

                } else if (data.data && data.data.length > 0) {
                    // Manual mode with single data structure
                    dataPreview.classList.remove('d-none');

                    // Create table header
                    const headers = Object.keys(data.data[0]);
                    let headerRow = '<tr>';
                    headers.forEach(header => {
                        headerRow += `<th>${header}</th>`;
                    });
                    headerRow += '</tr>';
                    previewTableHead.innerHTML = headerRow;

                    // Create table body
                    let bodyRows = '';
                    data.data.forEach(item => {
                        let row = '<tr>';
                        headers.forEach(header => {
                            row += `<td>${item[header] || ''}</td>`;
                        });
                        row += '</tr>';
                        bodyRows += row;
                    });
                    previewTableBody.innerHTML = bodyRows;

                    // Set download link
                    downloadLink.href = `/download/${data.file_path}`;
                    downloadLink.setAttribute('download', data.file_path);
                } else {
                    dataPreview.classList.add('d-none');
                    structuresPreview.classList.add('d-none');
                }
            } else {
                // Show error message
                resultMessage.classList.remove('alert-success');
                resultMessage.classList.add('alert-danger');
                resultMessage.textContent = data.message;
                dataPreview.classList.add('d-none');
                structuresPreview.classList.add('d-none');
            }
        })
        .catch(error => {
            // Hide loading indicator
            loadingIndicator.classList.add('d-none');
            resultsContainer.classList.remove('d-none');

            // Show error message
            resultMessage.classList.remove('alert-success');
            resultMessage.classList.add('alert-danger');
            resultMessage.textContent = 'An error occurred: ' + error.message;
            dataPreview.classList.add('d-none');
            structuresPreview.classList.add('d-none');
        });
    });
});
