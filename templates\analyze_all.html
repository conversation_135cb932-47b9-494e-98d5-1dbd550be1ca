<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analyze All - Options Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1400px;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .bullish {
            color: #28a745;
        }
        .bearish {
            color: #dc3545;
        }
        .neutral {
            color: #6c757d;
        }
        .progress-container {
            margin-top: 20px;
            margin-bottom: 20px;
        }
        .progress {
            height: 20px;
            border-radius: 10px;
        }
        .progress-bar {
            transition: width 0.5s ease;
        }
        .top-trade-card {
            border-left: 5px solid #007bff;
            transition: all 0.3s ease;
        }
        .top-trade-card.call {
            border-left-color: #28a745;
        }
        .top-trade-card.put {
            border-left-color: #dc3545;
        }
        .top-trade-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .score-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 1.2rem;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .watchlist-selector {
            margin-bottom: 20px;
        }
        .settings-container {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #f1f3f5;
        }
        .factor-weight {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .factor-weight label {
            flex: 1;
            margin-bottom: 0;
        }
        .factor-weight input {
            width: 60px;
            margin-left: 10px;
        }
        .filter-option {
            margin-bottom: 10px;
        }
        .analysis-status {
            font-weight: 500;
            margin-bottom: 10px;
        }
        .trade-details {
            margin-top: 15px;
        }
        .trade-detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        .trade-detail-label {
            font-weight: 500;
            color: #495057;
        }
        .trade-detail-value {
            font-weight: 500;
        }
        .trade-description {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #dee2e6;
            font-size: 0.9rem;
            color: #495057;
        }
        .money-flow-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
            margin-right: 5px;
        }
        .trend-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
            margin-right: 5px;
        }
        .expiry-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
            margin-right: 5px;
        }
        .no-results {
            text-align: center;
            padding: 50px 0;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark mb-4">
        <div class="container">
            <a class="navbar-brand" href="/">Options Analysis</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/options-analysis">Option Chain</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/analyze-all">Analyze All</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/token-manager">Token Manager</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h2 class="mb-4">Batch Analysis - Find Top Trade Opportunities</h2>

        <div class="row">
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Analysis Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="watchlist-selector mb-3">
                            <label for="watchlistSelect" class="form-label">Select Watchlist</label>
                            <select id="watchlistSelect" class="form-select">
                                <option value="NIFTY50">Nifty 50</option>
                                <option value="FNO">F&O Stocks</option>
                                <option value="INDICES">Indices</option>
                                <option value="CUSTOM">Custom</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="expirySelect" class="form-label">Expiry Date</label>
                            <select id="expirySelect" class="form-select">
                                <!-- Will be populated dynamically -->
                            </select>
                        </div>

                        <div class="settings-container">
                            <h6 class="mb-3">Factor Weights</h6>

                            <div class="factor-weight">
                                <label for="moneyFlowWeight">Money Flow</label>
                                <input type="number" id="moneyFlowWeight" class="form-control" value="30" min="0" max="100">
                                <span class="ms-1">%</span>
                            </div>

                            <div class="factor-weight">
                                <label for="oiWeight">OI Analysis</label>
                                <input type="number" id="oiWeight" class="form-control" value="30" min="0" max="100">
                                <span class="ms-1">%</span>
                            </div>

                            <div class="factor-weight">
                                <label for="trendWeight">Technical Trend</label>
                                <input type="number" id="trendWeight" class="form-control" value="20" min="0" max="100">
                                <span class="ms-1">%</span>
                            </div>

                            <div class="factor-weight">
                                <label for="pcrWeight">PCR</label>
                                <input type="number" id="pcrWeight" class="form-control" value="10" min="0" max="100">
                                <span class="ms-1">%</span>
                            </div>

                            <div class="factor-weight">
                                <label for="newsWeight">News Risk</label>
                                <input type="number" id="newsWeight" class="form-control" value="10" min="0" max="100">
                                <span class="ms-1">%</span>
                            </div>

                            <div class="total-weight mt-2 text-end">
                                <span id="totalWeight">Total: 100%</span>
                            </div>
                        </div>

                        <div class="settings-container">
                            <h6 class="mb-3">Filters</h6>

                            <div class="filter-option">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="liquidityFilter" checked>
                                    <label class="form-check-label" for="liquidityFilter">
                                        Filter Low Liquidity
                                    </label>
                                </div>
                                <div class="ms-4 mt-1">
                                    <label for="minVolume" class="form-label small">Min Volume</label>
                                    <input type="number" id="minVolume" class="form-control form-control-sm" value="500000">
                                </div>
                            </div>

                            <div class="filter-option">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="volatilityFilter" checked>
                                    <label class="form-check-label" for="volatilityFilter">
                                        Filter Low Volatility
                                    </label>
                                </div>
                                <div class="ms-4 mt-1">
                                    <label for="minVolatility" class="form-label small">Min Price Change %</label>
                                    <input type="number" id="minVolatility" class="form-control form-control-sm" value="1.0" step="0.1">
                                </div>
                            </div>

                            <div class="filter-option">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="conflictFilter" checked>
                                    <label class="form-check-label" for="conflictFilter">
                                        Avoid Conflicting Signals
                                    </label>
                                </div>
                            </div>
                        </div>

                        <button id="startAnalysisBtn" class="btn btn-primary w-100 mt-3">
                            <i class="bi bi-search me-2"></i>Start Analysis
                        </button>

                        <button id="cancelAnalysisBtn" class="btn btn-danger w-100 mt-2" style="display: none;">
                            <i class="bi bi-x-circle me-2"></i>Cancel Analysis
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">Top Trade Opportunities</h5>
                    </div>
                    <div class="card-body">
                        <div id="analysisStatus" class="analysis-status">
                            Ready to analyze. Select a watchlist and click "Start Analysis".
                        </div>

                        <div id="progressContainer" class="progress-container" style="display: none;">
                            <div class="progress">
                                <div id="analysisProgress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="text-center mt-2">
                                <span id="progressText">Analyzing 0 of 0 stocks (0%)</span>
                            </div>
                        </div>

                        <div id="resultsContainer">
                            <div id="noResultsMessage" class="no-results">
                                <i class="bi bi-search" style="font-size: 3rem;"></i>
                                <h4 class="mt-3">No Results Yet</h4>
                                <p>Start the analysis to find the best trade opportunities</p>
                            </div>

                            <!-- Results will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="loadingOverlay" class="loading-overlay d-none">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // DOM Elements
        const watchlistSelect = document.getElementById('watchlistSelect');
        const expirySelect = document.getElementById('expirySelect');
        const startAnalysisBtn = document.getElementById('startAnalysisBtn');
        const cancelAnalysisBtn = document.getElementById('cancelAnalysisBtn');
        const analysisStatus = document.getElementById('analysisStatus');
        const progressContainer = document.getElementById('progressContainer');
        const analysisProgress = document.getElementById('analysisProgress');
        const progressText = document.getElementById('progressText');
        const resultsContainer = document.getElementById('resultsContainer');
        const noResultsMessage = document.getElementById('noResultsMessage');
        const loadingOverlay = document.getElementById('loadingOverlay');

        // Weight inputs
        const moneyFlowWeight = document.getElementById('moneyFlowWeight');
        const oiWeight = document.getElementById('oiWeight');
        const trendWeight = document.getElementById('trendWeight');
        const pcrWeight = document.getElementById('pcrWeight');
        const newsWeight = document.getElementById('newsWeight');
        const totalWeight = document.getElementById('totalWeight');

        // Filter inputs
        const liquidityFilter = document.getElementById('liquidityFilter');
        const minVolume = document.getElementById('minVolume');
        const volatilityFilter = document.getElementById('volatilityFilter');
        const minVolatility = document.getElementById('minVolatility');
        const conflictFilter = document.getElementById('conflictFilter');

        // Variables
        let isAnalyzing = false;
        let progressInterval = null;
        let currentTaskId = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Populate expiry dates (next 4 weekly expiries)
            populateExpiryDates();

            // Add event listeners
            startAnalysisBtn.addEventListener('click', startAnalysis);
            cancelAnalysisBtn.addEventListener('click', cancelAnalysis);

            // Add event listeners for weight inputs
            moneyFlowWeight.addEventListener('input', updateTotalWeight);
            oiWeight.addEventListener('input', updateTotalWeight);
            trendWeight.addEventListener('input', updateTotalWeight);
            pcrWeight.addEventListener('input', updateTotalWeight);
            newsWeight.addEventListener('input', updateTotalWeight);

            // Load watchlists
            loadWatchlists();

            // Check for any active analysis tasks
            checkForActiveTasks();

            // Add event listener for page visibility changes
            document.addEventListener('visibilitychange', handleVisibilityChange);
        });

        // Populate expiry dates
        function populateExpiryDates() {
            const today = new Date();
            let thursday = new Date(today);

            // Find the next Thursday
            thursday.setDate(today.getDate() + ((4 + 4 - today.getDay()) % 7));

            // Add the next 4 Thursdays
            for (let i = 0; i < 4; i++) {
                const expiryDate = new Date(thursday);
                expiryDate.setDate(thursday.getDate() + (i * 7));

                const dateStr = formatDate(expiryDate);
                const option = document.createElement('option');
                option.value = dateStr;
                option.textContent = dateStr + (i === 0 ? ' (Current)' : '');

                expirySelect.appendChild(option);
            }
        }

        // Format date as DD-MM-YYYY
        function formatDate(date) {
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();

            return `${day}-${month}-${year}`;
        }

        // Load watchlists
        function loadWatchlists() {
            fetch('/api/watchlists')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Clear existing options
                        watchlistSelect.innerHTML = '';

                        // Add options for each watchlist
                        Object.keys(data.watchlists).forEach(name => {
                            const option = document.createElement('option');
                            option.value = name;
                            option.textContent = name;
                            watchlistSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading watchlists:', error);
                });
        }

        // Update total weight
        function updateTotalWeight() {
            const total = parseInt(moneyFlowWeight.value) +
                         parseInt(oiWeight.value) +
                         parseInt(trendWeight.value) +
                         parseInt(pcrWeight.value) +
                         parseInt(newsWeight.value);

            totalWeight.textContent = `Total: ${total}%`;

            // Highlight if not 100%
            if (total !== 100) {
                totalWeight.classList.add('text-danger');
                totalWeight.classList.remove('text-success');
            } else {
                totalWeight.classList.add('text-success');
                totalWeight.classList.remove('text-danger');
            }
        }

        // Handle visibility change (tab switching)
        function handleVisibilityChange() {
            if (document.visibilityState === 'visible') {
                // Page is now visible, check for active tasks
                if (currentTaskId) {
                    // Resume progress checking if we have an active task
                    if (!progressInterval) {
                        progressInterval = setInterval(checkProgress, 1000);
                    }
                } else {
                    // Check if there's any active task
                    checkForActiveTasks();
                }
            } else {
                // Page is now hidden, we can stop checking progress to save resources
                // but the analysis will continue in the background
                if (progressInterval) {
                    clearInterval(progressInterval);
                    progressInterval = null;
                }
            }
        }

        // Check for active tasks
        function checkForActiveTasks() {
            fetch('/api/batch-analysis/tasks')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.tasks.length > 0) {
                        // Check if there's a current task in the session
                        if (data.current_task) {
                            const task = data.tasks.find(t => t.task_id === data.current_task);
                            if (task && (task.status === 'running' || task.status === 'starting')) {
                                // Resume the active task
                                currentTaskId = task.task_id;
                                isAnalyzing = true;
                                startAnalysisBtn.style.display = 'none';
                                cancelAnalysisBtn.style.display = 'block';
                                progressContainer.style.display = 'block';
                                noResultsMessage.style.display = 'none';
                                analysisStatus.textContent = `Analysis in progress for ${task.watchlist}...`;

                                // Start progress checking
                                if (!progressInterval) {
                                    progressInterval = setInterval(checkProgress, 1000);
                                }
                            } else if (task && task.status === 'completed') {
                                // Load results for completed task
                                currentTaskId = task.task_id;
                                loadResults(task.task_id);
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('Error checking for active tasks:', error);
                });
        }

        // Start analysis
        function startAnalysis() {
            if (isAnalyzing) return;

            // Validate weights
            const total = parseInt(moneyFlowWeight.value) +
                         parseInt(oiWeight.value) +
                         parseInt(trendWeight.value) +
                         parseInt(pcrWeight.value) +
                         parseInt(newsWeight.value);

            if (total !== 100) {
                alert('Factor weights must add up to 100%');
                return;
            }

            // Get settings
            const settings = {
                min_volume: liquidityFilter.checked ? parseInt(minVolume.value) : 0,
                min_volatility: volatilityFilter.checked ? parseFloat(minVolatility.value) : 0,
                weights: {
                    money_flow: parseInt(moneyFlowWeight.value) / 100,
                    oi_analysis: parseInt(oiWeight.value) / 100,
                    trend: parseInt(trendWeight.value) / 100,
                    pcr: parseInt(pcrWeight.value) / 100,
                    news_risk: parseInt(newsWeight.value) / 100
                }
            };

            // Show loading state
            isAnalyzing = true;
            startAnalysisBtn.style.display = 'none';
            cancelAnalysisBtn.style.display = 'block';
            progressContainer.style.display = 'block';
            noResultsMessage.style.display = 'none';
            resultsContainer.innerHTML = '';
            analysisStatus.textContent = 'Analysis in progress...';

            // Call API to start analysis
            fetch('/api/batch-analysis/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    watchlist: watchlistSelect.value,
                    expiry_date: expirySelect.value,
                    settings: settings
                })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Store the task ID
                        currentTaskId = data.task_id;

                        // Start polling for progress
                        progressInterval = setInterval(checkProgress, 1000);
                    } else {
                        // Show error
                        analysisStatus.textContent = 'Error: ' + data.message;
                        resetAnalysisState();
                    }
                })
                .catch(error => {
                    analysisStatus.textContent = 'Error: ' + error.message;
                    resetAnalysisState();
                });
        }

        // Cancel analysis
        function cancelAnalysis() {
            if (!isAnalyzing || !currentTaskId) return;

            fetch('/api/batch-analysis/cancel', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    task_id: currentTaskId
                })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        analysisStatus.textContent = 'Analysis cancelled';
                        resetAnalysisState();
                    }
                })
                .catch(error => {
                    console.error('Error cancelling analysis:', error);
                });
        }

        // Check progress
        function checkProgress() {
            if (!currentTaskId) return;

            fetch(`/api/batch-analysis/progress?task_id=${currentTaskId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const progress = data.progress;

                        // Update progress bar
                        analysisProgress.style.width = progress.progress + '%';
                        progressText.textContent = `Analyzing ${progress.analyzed} of ${progress.total_stocks} stocks (${Math.round(progress.progress)}%)`;

                        // Add elapsed time to status
                        analysisStatus.textContent = `Analysis in progress for ${progress.watchlist}... (${progress.elapsed_time})`;

                        // Check if complete
                        if (progress.progress >= 100 || progress.status === 'completed' || !progress.is_analyzing) {
                            clearInterval(progressInterval);
                            progressInterval = null;

                            if (progress.progress >= 100 || progress.status === 'completed') {
                                if (progress.message && progress.message.includes("errors")) {
                                    analysisStatus.innerHTML = '<span class="text-warning">Analysis complete with errors. Some stocks could not be analyzed.</span>';
                                } else {
                                    analysisStatus.textContent = 'Analysis complete!';
                                }
                                loadResults(currentTaskId);
                            } else if (progress.status === 'cancelled') {
                                analysisStatus.textContent = 'Analysis cancelled';
                                resetAnalysisState();
                            } else {
                                analysisStatus.textContent = 'Analysis stopped';
                                resetAnalysisState();
                            }
                        }
                    } else {
                        // Task might have been deleted or expired
                        console.error('Error checking progress:', data.message);
                        resetAnalysisState();
                    }
                })
                .catch(error => {
                    console.error('Error checking progress:', error);
                });
        }

        // Load results
        function loadResults(taskId) {
            const id = taskId || currentTaskId;
            if (!id) return;

            fetch(`/api/batch-analysis/results?task_id=${id}&count=4`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayResults(data.results);

                        // Only reset analysis state if the task is complete
                        if (data.status === 'completed' || data.status === 'cancelled') {
                            resetAnalysisState();
                        }
                    } else {
                        console.error('Error loading results:', data.message);
                        resetAnalysisState();
                    }
                })
                .catch(error => {
                    console.error('Error loading results:', error);
                    resetAnalysisState();
                });
        }

        // Display results
        function displayResults(results) {
            // Clear results container
            resultsContainer.innerHTML = '';

            if (results.length === 0) {
                // Show no results message
                noResultsMessage.style.display = 'block';
                noResultsMessage.innerHTML = `
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">No Trade Opportunities Found</h5>
                        <p>We successfully fetched data from Fyers API, but no stocks passed our analysis criteria. This could be due to:</p>
                        <ul>
                            <li>Low market volatility today</li>
                            <li>Insufficient trading volume in options</li>
                            <li>No clear trading signals in the current market conditions</li>
                        </ul>
                        <p>Try again later or select a different watchlist.</p>
                    </div>
                `;
                analysisStatus.innerHTML = '<span class="text-warning">Data fetched successfully, but no trade opportunities met our criteria</span>';
                return;
            }

            // Create results header
            const header = document.createElement('h5');
            header.className = 'mb-4';
            header.textContent = `Top ${results.length} Trade Opportunities`;
            resultsContainer.appendChild(header);

            // Create results grid
            const grid = document.createElement('div');
            grid.className = 'row';

            // Add each result
            results.forEach((result, index) => {
                const trade = result.trade;
                const col = document.createElement('div');
                col.className = 'col-md-6 mb-4';

                const card = document.createElement('div');
                card.className = `card top-trade-card ${trade.option_type.toLowerCase()}`;

                // Card header
                const cardHeader = document.createElement('div');
                cardHeader.className = `card-header ${trade.option_type === 'CALL' || trade.option_type === 'CE' ? 'bg-success' : 'bg-danger'} text-white`;

                const headerTitle = document.createElement('h5');
                headerTitle.className = 'mb-0';

                // Extract the actual stock symbol from the full symbol string
                let displaySymbol = result.symbol;
                if (displaySymbol.includes(':')) {
                    displaySymbol = displaySymbol.split(':')[1];
                }
                if (displaySymbol.includes('-')) {
                    displaySymbol = displaySymbol.split('-')[0];
                }

                headerTitle.textContent = `#${index + 1}: ${displaySymbol} ${trade.option_type} @ ${trade.strike}`;

                cardHeader.appendChild(headerTitle);
                card.appendChild(cardHeader);

                // Score badge
                const scoreBadge = document.createElement('div');
                scoreBadge.className = 'badge bg-primary score-badge';
                scoreBadge.textContent = `Score: ${Math.round(result.score)}/100`;
                card.appendChild(scoreBadge);

                // No mock data badges - we don't use mock data anymore

                // Card body
                const cardBody = document.createElement('div');
                cardBody.className = 'card-body';

                // Current price
                const currentPrice = document.createElement('div');
                currentPrice.className = 'mb-3';
                currentPrice.innerHTML = `<strong>Current Price:</strong> ₹${result.current_price.toFixed(2)}`;
                cardBody.appendChild(currentPrice);

                // Badges
                const badgesContainer = document.createElement('div');
                badgesContainer.className = 'mb-3';

                // Money flow badge
                if (result.money_flow && result.money_flow.flow_bias) {
                    const moneyFlowBadge = document.createElement('span');
                    moneyFlowBadge.className = `money-flow-badge ${result.money_flow.flow_bias.toLowerCase() === 'bullish' ? 'bg-success' : (result.money_flow.flow_bias.toLowerCase() === 'bearish' ? 'bg-danger' : 'bg-secondary')} text-white`;
                    moneyFlowBadge.textContent = `${result.money_flow.flow_bias} Flow`;
                    badgesContainer.appendChild(moneyFlowBadge);
                }

                // Trend badge
                if (result.analysis && result.analysis.trend_data && result.analysis.trend_data.trend_bias) {
                    const trendBadge = document.createElement('span');
                    trendBadge.className = `trend-badge ${result.analysis.trend_data.trend_bias.toLowerCase().includes('bullish') ? 'bg-success' : (result.analysis.trend_data.trend_bias.toLowerCase().includes('bearish') ? 'bg-danger' : 'bg-secondary')} text-white`;
                    trendBadge.textContent = result.analysis.trend_data.trend_bias;
                    badgesContainer.appendChild(trendBadge);
                }

                // Expiry badge
                if (result.expiry_date) {
                    const expiryBadge = document.createElement('span');
                    expiryBadge.className = 'expiry-badge bg-info text-white';
                    expiryBadge.textContent = `Exp: ${result.expiry_date}`;
                    badgesContainer.appendChild(expiryBadge);
                }

                cardBody.appendChild(badgesContainer);

                // Trade details
                const tradeDetails = document.createElement('div');
                tradeDetails.className = 'trade-details';

                // Buy price
                addTradeDetailRow(tradeDetails, 'Buy Price', `₹${trade.buy_price.toFixed(2)}`);

                // Target price
                addTradeDetailRow(tradeDetails, 'Target', `₹${trade.exit_target.toFixed(2)}`);

                // Stop loss
                addTradeDetailRow(tradeDetails, 'Stop Loss', `₹${trade.stop_loss.toFixed(2)}`);

                // Risk-reward
                addTradeDetailRow(tradeDetails, 'Risk:Reward', `1:${trade.risk_reward.toFixed(2)}`);

                // IV
                addTradeDetailRow(tradeDetails, 'IV', `${trade.iv.toFixed(2)}%`);

                // Volume
                addTradeDetailRow(tradeDetails, 'Volume', trade.volume.toLocaleString());

                // OI Change
                addTradeDetailRow(tradeDetails, 'OI Change', trade.oi_change.toLocaleString());

                cardBody.appendChild(tradeDetails);

                // Trade description
                if (trade.description) {
                    const description = document.createElement('div');
                    description.className = 'trade-description';
                    description.textContent = trade.description;
                    cardBody.appendChild(description);
                }

                card.appendChild(cardBody);
                col.appendChild(card);
                grid.appendChild(col);
            });

            resultsContainer.appendChild(grid);

                // Update status
            analysisStatus.textContent = `Found ${results.length} trade opportunities`;
        }

        // Add trade detail row
        function addTradeDetailRow(container, label, value) {
            const row = document.createElement('div');
            row.className = 'trade-detail-row';

            const labelEl = document.createElement('div');
            labelEl.className = 'trade-detail-label';
            labelEl.textContent = label;

            const valueEl = document.createElement('div');
            valueEl.className = 'trade-detail-value';
            valueEl.textContent = value;

            row.appendChild(labelEl);
            row.appendChild(valueEl);
            container.appendChild(row);
        }

        // Reset analysis state
        function resetAnalysisState() {
            isAnalyzing = false;
            currentTaskId = null;

            if (progressInterval) {
                clearInterval(progressInterval);
                progressInterval = null;
            }

            startAnalysisBtn.style.display = 'block';
            cancelAnalysisBtn.style.display = 'none';
            progressContainer.style.display = 'none';
        }

        // Show loading
        function showLoading() {
            loadingOverlay.classList.remove('d-none');
        }

        // Hide loading
        function hideLoading() {
            loadingOverlay.classList.add('d-none');
        }
    </script>
</body>
</html>
