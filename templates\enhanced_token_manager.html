<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Token Manager - Fyers Integration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .step-card {
            border-left: 4px solid #007bff;
            margin-bottom: 20px;
        }
        .token-preview {
            font-family: monospace;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            word-break: break-all;
        }
        .auth-url {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <nav class="navbar navbar-expand-lg navbar-dark bg-dark mb-4">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="/">
                            <i class="fas fa-chart-line"></i> Fyers Options Analysis
                        </a>
                        <div class="navbar-nav ms-auto">
                            <a class="nav-link" href="/">Home</a>
                            <a class="nav-link" href="/options-analysis">Traditional Analysis</a>
                            <a class="nav-link" href="/ai-options-analysis">AI Analysis</a>
                            <a class="nav-link active" href="/token-manager">Token Manager</a>
                        </div>
                    </div>
                </nav>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-key"></i> Enhanced Fyers API Token Manager</h4>
                        <p class="mb-0">Generate and manage your Fyers API access token for real-time data access</p>
                    </div>
                    <div class="card-body">
                        <!-- Current Token Status -->
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle"></i> Current Status</h5>
                            <p><strong>Your refresh token is configured but needs to be converted to an access token.</strong></p>
                            <p>Follow the steps below to generate a working access token.</p>
                        </div>

                        <!-- Step-by-Step Token Generation -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5><i class="fas fa-list-ol"></i> Step-by-Step Token Generation</h5>
                                
                                <!-- Step 1 -->
                                <div class="card step-card">
                                    <div class="card-body">
                                        <h6><i class="fas fa-globe"></i> Step 1: Open Authentication URL</h6>
                                        <p>Click the button below to open the Fyers authentication page in a new tab:</p>
                                        <div class="auth-url mb-3">
                                            <strong>Authentication URL:</strong><br>
                                            <small>https://api-t1.fyers.in/api/v3/generate-authcode?client_id=B5QJCSZE9E-100&redirect_uri=https://trade.fyers.in/api-login/redirect-to-app&response_type=code&state=sample_state</small>
                                        </div>
                                        <a href="https://api-t1.fyers.in/api/v3/generate-authcode?client_id=B5QJCSZE9E-100&redirect_uri=https://trade.fyers.in/api-login/redirect-to-app&response_type=code&state=sample_state" 
                                           target="_blank" class="btn btn-primary btn-lg">
                                            <i class="fas fa-external-link-alt"></i> Open Fyers Authentication
                                        </a>
                                    </div>
                                </div>

                                <!-- Step 2 -->
                                <div class="card step-card">
                                    <div class="card-body">
                                        <h6><i class="fas fa-sign-in-alt"></i> Step 2: Login and Get Auth Code</h6>
                                        <ol>
                                            <li>Login with your Fyers credentials on the opened page</li>
                                            <li>After successful login, you'll be redirected to a URL that looks like:</li>
                                        </ol>
                                        <div class="token-preview mb-3">
                                            <code>https://trade.fyers.in/api-login/redirect-to-app?auth_code=<span class="text-danger">YOUR_AUTH_CODE_HERE</span>&state=sample_state</code>
                                        </div>
                                        <p><strong>Copy the auth_code value</strong> from the URL (the part after "auth_code=" and before "&state")</p>
                                    </div>
                                </div>

                                <!-- Step 3 -->
                                <div class="card step-card">
                                    <div class="card-body">
                                        <h6><i class="fas fa-key"></i> Step 3: Generate Access Token</h6>
                                        <p>Paste the auth_code from Step 2 and click "Generate Access Token":</p>
                                        
                                        <div class="row">
                                            <div class="col-md-8">
                                                <input type="text" class="form-control" id="auth_code" 
                                                       placeholder="Paste your auth_code here (e.g., 200001234567890abcdef...)">
                                            </div>
                                            <div class="col-md-4">
                                                <button type="button" class="btn btn-success btn-lg w-100" onclick="generateAccessToken()">
                                                    <i class="fas fa-cog"></i> Generate Access Token
                                                </button>
                                            </div>
                                        </div>
                                        <div id="tokenGenerationResult" class="mt-3"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Alternative: Manual Token Entry -->
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-edit"></i> Alternative: Manual Token Entry</h6>
                            </div>
                            <div class="card-body">
                                <p>If you already have a valid access token, you can enter it directly:</p>
                                <form id="tokenForm">
                                    <div class="mb-3">
                                        <label for="access_token" class="form-label">Access Token</label>
                                        <textarea class="form-control" id="access_token" rows="3" 
                                                  placeholder="Enter your Fyers API access token here..."></textarea>
                                        <div class="form-text">
                                            <i class="fas fa-info-circle"></i> Access tokens are valid for 24 hours. 
                                            Get yours from <a href="https://myapi.fyers.in/" target="_blank">Fyers API Portal</a>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Save Token
                                    </button>
                                </form>
                            </div>
                        </div>

                        <div id="result" class="mt-3"></div>

                        <!-- Help Section -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h6><i class="fas fa-question-circle"></i> Need Help?</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>Common Issues:</strong></p>
                                <ul>
                                    <li><strong>Auth code not working:</strong> Make sure you copied the complete auth_code from the URL</li>
                                    <li><strong>Login failed:</strong> Verify your Fyers credentials and try again</li>
                                    <li><strong>Token expired:</strong> Access tokens expire after 24 hours, generate a new one</li>
                                </ul>
                                <p><strong>Your App Details:</strong></p>
                                <ul>
                                    <li>App ID: B5QJCSZE9E-100</li>
                                    <li>Secret ID: T8EICCWNZZ</li>
                                    <li>Redirect URI: https://trade.fyers.in/api-login/redirect-to-app</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Generate access token from auth code
        function generateAccessToken() {
            const authCode = document.getElementById('auth_code').value.trim();
            const resultDiv = document.getElementById('tokenGenerationResult');
            
            if (!authCode) {
                resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-times"></i> Please enter the auth_code from Step 2</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> Generating access token...</div>';
            
            // Make API call to generate token
            fetch('/generate-access-token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    auth_code: authCode,
                    client_id: 'B5QJCSZE9E-100',
                    secret_key: 'T8EICCWNZZ'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check"></i> Access Token Generated Successfully!</h6>
                            <div class="token-preview mt-2">
                                <strong>Access Token:</strong> ${data.access_token.substring(0, 50)}...
                            </div>
                            <p class="mt-2"><i class="fas fa-info-circle"></i> Token has been automatically saved. You can now use the application!</p>
                            <a href="/ai-options-analysis" class="btn btn-primary mt-2">
                                <i class="fas fa-robot"></i> Try AI Analysis
                            </a>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-times"></i> Token Generation Failed</h6>
                            <p>${data.message}</p>
                            <p><strong>Troubleshooting:</strong></p>
                            <ul>
                                <li>Make sure you copied the complete auth_code from the URL</li>
                                <li>The auth_code should be around 20-30 characters long</li>
                                <li>Try generating a new auth_code if this one doesn't work</li>
                            </ul>
                        </div>
                    `;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-times"></i> Network Error</h6>
                        <p>Error: ${error.message}</p>
                        <p>Please check your internet connection and try again.</p>
                    </div>
                `;
            });
        }

        // Manual token form submission
        document.getElementById('tokenForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const accessToken = document.getElementById('access_token').value.trim();
            const resultDiv = document.getElementById('result');
            
            if (!accessToken) {
                resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-times"></i> Please enter an access token</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> Saving token...</div>';
            
            fetch('/update-token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'access_token=' + encodeURIComponent(accessToken)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check"></i> Token Saved Successfully!</h6>
                            <p>${data.message}</p>
                            <a href="/ai-options-analysis" class="btn btn-primary mt-2">
                                <i class="fas fa-robot"></i> Try AI Analysis
                            </a>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-times"></i> ' + data.message + '</div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-times"></i> Error: ' + error.message + '</div>';
            });
        });
    </script>
</body>
</html>
