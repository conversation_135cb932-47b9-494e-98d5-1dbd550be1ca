#!/usr/bin/env python3
"""
Generate access token from refresh token for Fyers API
"""

import os
import json
import time
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def generate_access_token():
    """Generate access token from refresh token"""
    print("🔄 Generating Access Token from Refresh Token...")
    
    try:
        from fyers_apiv3 import fyersModel
        
        # Get credentials
        app_id = os.environ.get('FYERS_APP_ID')
        secret_id = os.environ.get('FYERS_SECRET_ID')
        refresh_token = os.environ.get('FYERS_ACCESS_TOKEN')  # This is actually the refresh token
        
        if not all([app_id, secret_id, refresh_token]):
            print("❌ Missing required credentials")
            return None
        
        print(f"✅ App ID: {app_id}")
        print(f"✅ Secret ID: {secret_id[:5]}...")
        print(f"✅ Refresh Token: {refresh_token[:50]}...")
        
        # Create session model for token generation
        session = fyersModel.SessionModel(
            client_id=app_id,
            secret_key=secret_id,
            redirect_uri="https://trade.fyers.in/api-login/redirect-to-app",
            response_type="code",
            grant_type="authorization_code"
        )
        
        print("🔄 Generating access token...")
        
        # Generate access token from refresh token
        response = session.generate_token()
        
        if response and response.get('s') == 'ok':
            access_token = response.get('access_token')
            print("✅ Access token generated successfully!")
            print(f"📝 Access Token: {access_token[:50]}...")
            
            # Save to environment file
            save_access_token(access_token)
            
            # Test the new token
            test_new_token(access_token)
            
            return access_token
        else:
            print(f"❌ Failed to generate access token: {response}")
            
            # Try alternative method using refresh token directly
            print("\n🔄 Trying alternative method...")
            return generate_token_alternative()
            
    except Exception as e:
        print(f"❌ Error generating access token: {str(e)}")
        print("\n🔄 Trying alternative method...")
        return generate_token_alternative()

def generate_token_alternative():
    """Alternative method to generate token"""
    try:
        from fyers_apiv3 import fyersModel
        
        app_id = os.environ.get('FYERS_APP_ID')
        secret_id = os.environ.get('FYERS_SECRET_ID')
        refresh_token = os.environ.get('FYERS_ACCESS_TOKEN')
        
        # Try using the refresh token directly with the API
        session = fyersModel.SessionModel(
            client_id=app_id,
            secret_key=secret_id,
            redirect_uri="https://trade.fyers.in/api-login/redirect-to-app",
            response_type="code",
            grant_type="authorization_code"
        )
        
        # Set the refresh token
        session.set_token(refresh_token)
        
        # Try to generate access token
        response = session.generate_token()
        
        if response and response.get('s') == 'ok':
            access_token = response.get('access_token')
            print("✅ Access token generated using alternative method!")
            print(f"📝 Access Token: {access_token[:50]}...")
            
            save_access_token(access_token)
            test_new_token(access_token)
            
            return access_token
        else:
            print(f"❌ Alternative method also failed: {response}")
            print("\n💡 The refresh token might need to be used through the web interface.")
            print("   Please follow the manual token generation steps.")
            return None
            
    except Exception as e:
        print(f"❌ Alternative method error: {str(e)}")
        return None

def save_access_token(access_token):
    """Save access token to files"""
    try:
        # Update .env file
        env_content = []
        env_file = '.env'
        
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                env_content = f.readlines()
        
        # Update or add the access token line
        updated = False
        for i, line in enumerate(env_content):
            if line.startswith('FYERS_ACCESS_TOKEN='):
                env_content[i] = f'FYERS_ACCESS_TOKEN={access_token}\n'
                updated = True
                break
        
        if not updated:
            env_content.append(f'FYERS_ACCESS_TOKEN={access_token}\n')
        
        with open(env_file, 'w') as f:
            f.writelines(env_content)
        
        print(f"✅ Updated .env file with new access token")
        
        # Update token file
        token_file = 'tokens/fyers_token.json'
        os.makedirs('tokens', exist_ok=True)
        
        with open(token_file, 'w') as f:
            json.dump({
                "access_token": access_token,
                "timestamp": time.time()
            }, f)
        
        print(f"✅ Updated token file: {token_file}")
        
    except Exception as e:
        print(f"❌ Error saving access token: {str(e)}")

def test_new_token(access_token):
    """Test the new access token"""
    try:
        from fyers_apiv3 import fyersModel
        
        app_id = os.environ.get('FYERS_APP_ID')
        
        # Create client with new token
        fyers = fyersModel.FyersModel(
            client_id=app_id,
            token=access_token,
            is_async=False,
            log_path=""
        )
        
        print("🔄 Testing new access token...")
        
        # Test profile API
        response = fyers.get_profile()
        
        if response and response.get('s') == 'ok':
            print("✅ New access token is working!")
            profile_data = response.get('data', {})
            print(f"📊 User ID: {profile_data.get('fy_id', 'Unknown')}")
            return True
        else:
            print(f"❌ New access token test failed: {response}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing new token: {str(e)}")
        return False

def manual_token_instructions():
    """Provide manual token generation instructions"""
    print("\n📋 MANUAL TOKEN GENERATION STEPS:")
    print("=" * 50)
    print("Since automatic token generation failed, please follow these steps:")
    print()
    print("1. Open your browser and go to:")
    print("   https://api-t1.fyers.in/api/v3/generate-authcode")
    print()
    print("2. Use these parameters:")
    print(f"   client_id: {os.environ.get('FYERS_APP_ID', 'B5QJCSZE9E-100')}")
    print("   redirect_uri: https://trade.fyers.in/api-login/redirect-to-app")
    print("   response_type: code")
    print("   state: sample_state")
    print()
    print("3. Login with your Fyers credentials")
    print("4. After successful login, you'll get redirected to a URL with 'auth_code'")
    print("5. Copy the auth_code from the URL")
    print("6. Use the auth_code to generate access token via API")
    print()
    print("Or use the Fyers API documentation:")
    print("https://myapi.fyers.in/docsv3/#generate-access-token")

def main():
    """Main function"""
    print("🔑 Fyers Access Token Generator")
    print("=" * 40)
    
    # Try to generate access token
    access_token = generate_access_token()
    
    if access_token:
        print("\n🎉 SUCCESS! Access token generated and saved.")
        print("🔄 Please restart your application to use the new token.")
        print("🌐 Test your application at: http://127.0.0.1:5000")
    else:
        print("\n❌ Automatic token generation failed.")
        manual_token_instructions()
        
    return access_token is not None

if __name__ == "__main__":
    main()
