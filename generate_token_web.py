#!/usr/bin/env python3
"""
Web-based token generation for Fyers API
"""

import os
import webbrowser
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def generate_auth_url():
    """Generate the authorization URL for Fyers API"""
    
    app_id = os.environ.get('FYERS_APP_ID')
    if not app_id:
        print("❌ FYERS_APP_ID not found in environment variables")
        return None
    
    # Fyers authorization URL
    auth_url = (
        f"https://api-t1.fyers.in/api/v3/generate-authcode"
        f"?client_id={app_id}"
        f"&redirect_uri=https://trade.fyers.in/api-login/redirect-to-app"
        f"&response_type=code"
        f"&state=sample_state"
    )
    
    return auth_url

def main():
    """Main function to generate token"""
    print("🔑 Fyers Token Generation (Web Method)")
    print("=" * 50)
    
    # Generate auth URL
    auth_url = generate_auth_url()
    if not auth_url:
        return
    
    print("📋 STEPS TO GENERATE NEW ACCESS TOKEN:")
    print("=" * 50)
    print("1. The authorization URL will open in your browser")
    print("2. <PERSON>gin with your Fyers credentials")
    print("3. After successful login, you'll be redirected")
    print("4. Copy the 'auth_code' from the redirected URL")
    print("5. Use the Token Manager in the web app to generate access token")
    print()
    print("🌐 Authorization URL:")
    print(auth_url)
    print()
    
    # Ask user if they want to open the URL
    response = input("Do you want to open this URL in your browser? (y/n): ").strip().lower()
    
    if response in ['y', 'yes']:
        try:
            webbrowser.open(auth_url)
            print("✅ Browser opened with authorization URL")
        except Exception as e:
            print(f"❌ Error opening browser: {str(e)}")
            print("Please manually copy and paste the URL into your browser")
    
    print()
    print("📝 NEXT STEPS:")
    print("1. Complete the authorization in your browser")
    print("2. Copy the auth_code from the redirect URL")
    print("3. Go to http://localhost:5000/token-manager")
    print("4. Paste the auth_code and click 'Generate Access Token'")
    print("5. The new token will be automatically saved")

if __name__ == "__main__":
    main()
