#!/usr/bin/env python3
"""
Test script to check API responses
"""

import requests
import json

def test_option_chain_api():
    """Test the option chain API response"""
    print("🔄 Testing Option Chain API Response...")
    
    try:
        # Test the API endpoint
        url = "http://localhost:5000/api/fyers-option-chain"
        params = {
            "symbol": "NSE:NIFTY50-INDEX",
            "strike_count": 5
        }
        
        print(f"📡 Making request to: {url}")
        print(f"📋 Parameters: {params}")
        
        response = requests.get(url, params=params)
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📄 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ JSON Response received")
                print(f"📋 Success: {data.get('success', 'Not specified')}")
                print(f"📋 Message: {data.get('message', 'No message')}")
                
                if data.get('success'):
                    print(f"📊 Data length: {len(data.get('data', []))}")
                    print(f"📊 Underlying value: {data.get('underlying_value', 'N/A')}")
                    print(f"📊 Symbol: {data.get('symbol', 'N/A')}")
                    print(f"📊 Has real OI data: {data.get('has_real_oi_data', 'N/A')}")
                else:
                    print(f"❌ API returned success=False")
                    print(f"❌ Error message: {data.get('message', 'Unknown error')}")
                
                # Print first few data items if available
                if data.get('data') and len(data['data']) > 0:
                    print("\n📋 Sample data (first 2 items):")
                    for i, item in enumerate(data['data'][:2]):
                        print(f"  Item {i+1}: {json.dumps(item, indent=2)}")
                
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON: {str(e)}")
                print(f"📄 Raw response: {response.text[:500]}...")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the Flask app running?")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_fyers_stocks_api():
    """Test the fyers stocks API"""
    print("\n🔄 Testing Fyers Stocks API...")
    
    try:
        url = "http://localhost:5000/api/fyers-stocks"
        response = requests.get(url)
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📋 Success: {data.get('success', 'Not specified')}")
            if data.get('success'):
                stocks = data.get('stocks', [])
                print(f"📊 Number of stocks: {len(stocks)}")
                print(f"📋 Sample stocks: {stocks[:5]}")
            else:
                print(f"❌ Error: {data.get('message', 'Unknown error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🧪 API Response Testing")
    print("=" * 50)
    
    test_fyers_stocks_api()
    test_option_chain_api()
