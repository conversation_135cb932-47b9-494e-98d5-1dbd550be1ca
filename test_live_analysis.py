#!/usr/bin/env python3
"""
Test the new live options analysis system
"""

import json
from live_options_calculator import LiveOptionsCalculator
from gemini_ai_client import GeminiOptionsAnalyzer

def test_live_analysis():
    """Test the live analysis with sample data"""
    
    # Sample option chain data (similar to what Fyers API returns)
    sample_options_data = [
        {
            'Strike Price': 24000,
            'Option Type': 'CE',
            'OI': 15000,
            'Volume': 2500,
            'LTP': 125.50,
            'Change': 8.25
        },
        {
            'Strike Price': 24000,
            'Option Type': 'PE',
            'OI': 18000,
            'Volume': 3200,
            'LTP': 98.75,
            'Change': -12.30
        },
        {
            'Strike Price': 24100,
            'Option Type': 'CE',
            'OI': 12000,
            'Volume': 1800,
            'LTP': 85.25,
            'Change': 5.50
        },
        {
            'Strike Price': 24100,
            'Option Type': 'PE',
            'OI': 14500,
            'Volume': 2100,
            'LTP': 135.80,
            'Change': -8.75
        },
        {
            'Strike Price': 23900,
            'Option Type': 'CE',
            'OI': 20000,
            'Volume': 4500,
            'LTP': 165.30,
            'Change': 12.80
        },
        {
            'Strike Price': 23900,
            'Option Type': 'PE',
            'OI': 16500,
            'Volume': 2800,
            'LTP': 68.45,
            'Change': -15.20
        }
    ]
    
    current_price = 24050
    symbol = "NSE:NIFTY50-INDEX"
    days_to_expiry = 7
    
    print("🧪 Testing Live Options Analysis System")
    print("=" * 50)
    
    # Test 1: Live Calculator Only
    print("\n1️⃣ Testing Live Calculator...")
    calculator = LiveOptionsCalculator()
    live_result = calculator.calculate_comprehensive_analysis(
        sample_options_data, current_price, days_to_expiry
    )
    
    if live_result.get('success'):
        print("✅ Live calculations successful!")
        
        # Display key results
        pcr = live_result.get('pcr_analysis', {})
        print(f"📊 PCR (OI): {pcr.get('pcr_oi', 'N/A')}")
        print(f"📊 PCR Interpretation: {pcr.get('interpretation', 'N/A')}")
        
        max_pain = live_result.get('max_pain_analysis', {})
        print(f"🎯 Max Pain Strike: {max_pain.get('max_pain_strike', 'N/A')}")
        
        sentiment = live_result.get('market_sentiment', {})
        print(f"💭 Market Sentiment: {sentiment.get('overall_sentiment', 'N/A')} ({sentiment.get('confidence_level', 'N/A')}%)")
        
        recommendations = live_result.get('trade_recommendations', [])
        print(f"💡 Trade Recommendations: {len(recommendations)} generated")
        
        for i, rec in enumerate(recommendations, 1):
            if 'error' not in rec:
                print(f"   Trade {i}: {rec.get('type')} {rec.get('strike')} @ ₹{rec.get('entry_price')}")
    else:
        print(f"❌ Live calculations failed: {live_result.get('error')}")
    
    # Test 2: Full AI Analysis (if API key available)
    print("\n2️⃣ Testing Full AI Analysis...")
    try:
        ai_analyzer = GeminiOptionsAnalyzer()
        ai_result = ai_analyzer.analyze_option_chain(
            sample_options_data, current_price, symbol, days_to_expiry
        )
        
        if ai_result.get('success'):
            print("✅ AI analysis successful!")
            print(f"🤖 Analysis Type: {ai_result.get('metadata', {}).get('analysis_type', 'Unknown')}")
            
            if 'ai_insights' in ai_result:
                insights = ai_result['ai_insights'][:200] + "..." if len(ai_result['ai_insights']) > 200 else ai_result['ai_insights']
                print(f"💡 AI Insights: {insights}")
        else:
            print(f"❌ AI analysis failed: {ai_result.get('error')}")
            
    except Exception as e:
        print(f"⚠️ AI analysis not available: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🎯 Test completed!")
    
    return live_result

if __name__ == "__main__":
    test_live_analysis()
