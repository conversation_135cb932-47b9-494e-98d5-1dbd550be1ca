<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fyers Option Chain Viewer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1400px;
            margin-top: 20px;
        }

        .option-chain-container {
            overflow-x: auto;
            margin-top: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }

        .option-chain-table {
            font-size: 0.85rem;
            width: 100%;
            border-collapse: collapse;
        }

        .call-section {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .put-section {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .strike-price {
            background-color: #e9ecef;
            font-weight: bold;
        }

        .atm-strike {
            background-color: #ffcdd2 !important;
            font-weight: bold;
            color: #d32f2f;
        }

        .positive-change {
            color: #4caf50;
        }

        .negative-change {
            color: #f44336;
        }

        .in-the-money {
            background-color: rgba(255, 243, 224, 0.5);
        }

        .timestamp {
            font-size: 0.8rem;
            color: #666;
            margin-top: 5px;
        }

        .form-select {
            max-width: 300px;
        }

        .auth-section {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 8px;
            background-color: #e9ecef;
        }

        .strike-count-input {
            max-width: 100px;
        }

        .expiry-badge {
            margin-right: 5px;
            margin-bottom: 5px;
            cursor: pointer;
        }

        .expiry-badge.active {
            background-color: #0d6efd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Fyers Option Chain Viewer</h1>

        <div class="mb-3">
            <a href="/options-analysis" class="btn btn-primary">
                <i class="bi bi-graph-up"></i> Advanced Options Analysis
            </a>
        </div>

        <div class="auth-section">
            <h4>Authentication</h4>
            <p>You need to authenticate with Fyers to access the option chain data.</p>
            <div class="d-flex gap-2">
                <a href="/fyers-auth" class="btn btn-primary">Authenticate with Fyers</a>
                <a href="/fyers-manual-auth" class="btn btn-secondary">Manual Authentication</a>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-md-4">
                <label for="stockSelect" class="form-label">Select Stock/Index:</label>
                <select id="stockSelect" class="form-select">
                    <option value="">Select a stock or index</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="strikeCountInput" class="form-label">Strike Count:</label>
                <input type="number" id="strikeCountInput" class="form-control strike-count-input" value="5" min="1" max="50">
            </div>
            <div class="col-md-5 d-flex align-items-end">
                <button id="fetchBtn" class="btn btn-primary">Fetch Option Chain</button>
            </div>
        </div>

        <div id="expiryDatesContainer" class="mb-3 d-none">
            <label class="form-label">Expiry Dates:</label>
            <div id="expiryDates" class="d-flex flex-wrap">
                <!-- Expiry dates will be populated here -->
            </div>
        </div>

        <div id="loadingIndicator" class="alert alert-info d-none">
            <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                <span>Loading option chain data...</span>
            </div>
        </div>

        <div id="errorMessage" class="alert alert-danger d-none"></div>

        <div id="stockInfo" class="alert alert-info d-none">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5 id="stockSymbol" class="mb-0"></h5>
                    <small>Underlying Value: <span id="underlyingValue"></span></small>
                    <div class="timestamp" id="timestamp"></div>
                </div>
                <div>
                    <button id="downloadBtn" class="btn btn-sm btn-success d-none">Download CSV</button>
                    <button id="refreshBtn" class="btn btn-sm btn-primary ms-2">Refresh Data</button>
                </div>
            </div>
        </div>

        <div id="debugInfo" class="alert alert-secondary d-none mb-3">
            <h6>Debug Information</h6>
            <div id="debugContent"></div>
        </div>

        <div id="optionChainContainer" class="option-chain-container d-none">
            <table class="table table-bordered table-striped option-chain-table">
                <thead>
                    <tr>
                        <th colspan="9" class="text-center bg-primary text-white">CALLS</th>
                        <th class="text-center bg-dark text-white">STRIKE</th>
                        <th colspan="9" class="text-center bg-danger text-white">PUTS</th>
                    </tr>
                    <tr>
                        <th>OI</th>
                        <th>CHNG IN OI</th>
                        <th>VOLUME</th>
                        <th>IV</th>
                        <th>LTP</th>
                        <th>CHNG</th>
                        <th>BID QTY</th>
                        <th>BID</th>
                        <th>ASK</th>
                        <th class="text-center">STRIKE</th>
                        <th>BID QTY</th>
                        <th>BID</th>
                        <th>ASK</th>
                        <th>ASK QTY</th>
                        <th>CHNG</th>
                        <th>LTP</th>
                        <th>IV</th>
                        <th>VOLUME</th>
                        <th>CHNG IN OI</th>
                        <th>OI</th>
                    </tr>
                </thead>
                <tbody id="optionChainBody">
                    <!-- Option chain data will be populated here -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // DOM elements
            const stockSelect = document.getElementById('stockSelect');
            const strikeCountInput = document.getElementById('strikeCountInput');
            const fetchBtn = document.getElementById('fetchBtn');
            const expiryDatesContainer = document.getElementById('expiryDatesContainer');
            const expiryDates = document.getElementById('expiryDates');
            const stockInfo = document.getElementById('stockInfo');
            const stockSymbol = document.getElementById('stockSymbol');
            const underlyingValue = document.getElementById('underlyingValue');
            const timestamp = document.getElementById('timestamp');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const errorMessage = document.getElementById('errorMessage');
            const optionChainContainer = document.getElementById('optionChainContainer');
            const optionChainBody = document.getElementById('optionChainBody');
            const downloadBtn = document.getElementById('downloadBtn');
            const refreshBtn = document.getElementById('refreshBtn');
            const debugInfo = document.getElementById('debugInfo');
            const debugContent = document.getElementById('debugContent');

            let currentSymbol = '';
            let currentStrikeCount = 5;
            let currentFilePath = '';
            let selectedExpiry = '';

            // Load stocks on page load
            loadStocks();

            // Event listeners
            fetchBtn.addEventListener('click', function() {
                const symbol = stockSelect.value;
                const strikeCount = parseInt(strikeCountInput.value) || 5;

                if (symbol) {
                    currentSymbol = symbol;
                    currentStrikeCount = strikeCount;
                    loadOptionChain(symbol, strikeCount);
                } else {
                    showError('Please select a stock or index');
                }
            });

            refreshBtn.addEventListener('click', function() {
                if (currentSymbol) {
                    loadOptionChain(currentSymbol, currentStrikeCount);
                }
            });

            downloadBtn.addEventListener('click', function() {
                if (currentFilePath) {
                    window.location.href = `/download/${currentFilePath}`;
                }
            });

            // Functions
            function loadStocks() {
                showLoading();

                fetch('/api/fyers-stocks')
                    .then(response => response.json())
                    .then(data => {
                        hideLoading();

                        if (data.success && data.stocks) {
                            // Clear existing options
                            stockSelect.innerHTML = '<option value="">Select a stock or index</option>';

                            // Add new options
                            data.stocks.forEach(stock => {
                                const option = document.createElement('option');
                                option.value = stock;
                                option.textContent = stock;
                                stockSelect.appendChild(option);
                            });
                        } else {
                            showError('Failed to load stocks');
                        }
                    })
                    .catch(error => {
                        hideLoading();
                        showError('Error loading stocks: ' + error.message);
                    });
            }

            function loadOptionChain(symbol, strikeCount) {
                showLoading();
                hideError();

                // Clear existing data
                optionChainBody.innerHTML = '';
                optionChainContainer.classList.add('d-none');
                stockInfo.classList.add('d-none');
                expiryDatesContainer.classList.add('d-none');

                console.log(`Fetching option chain for ${symbol} with strike count ${strikeCount}`);

                fetch(`/api/fyers-option-chain?symbol=${encodeURIComponent(symbol)}&strike_count=${strikeCount}`)
                    .then(response => response.json())
                    .then(data => {
                        hideLoading();
                        console.log("Response received:", data);

                        // Show debug information
                        debugInfo.classList.remove('d-none');
                        debugContent.innerHTML = `
                            <p>Response status: ${data.success ? 'Success' : 'Failed'}</p>
                            <p>Symbol: ${data.symbol || 'N/A'}</p>
                            <p>Underlying value: ${data.underlying_value || 'N/A'}</p>
                            <p>Number of options: ${data.data ? data.data.length : 0}</p>
                            <p>First few options: ${JSON.stringify(data.data ? data.data.slice(0, 2) : []).substring(0, 200)}...</p>
                        `;

                        if (data.success && data.data) {
                            console.log(`Received ${data.data.length} options`);

                            // Update stock info
                            stockSymbol.textContent = data.symbol;
                            underlyingValue.textContent = data.underlying_value;

                            // Display timestamp if available
                            if (data.timestamp) {
                                timestamp.textContent = `Last updated: ${data.timestamp}`;
                            } else {
                                timestamp.textContent = `Last updated: ${new Date().toLocaleString()}`;
                            }

                            stockInfo.classList.remove('d-none');

                            // Set download link
                            if (data.file_path) {
                                currentFilePath = data.file_path;
                                downloadBtn.classList.remove('d-none');
                            } else {
                                downloadBtn.classList.add('d-none');
                            }

                            // Display expiry dates if available
                            if (data.expiry_dates && data.expiry_dates.length > 0) {
                                expiryDates.innerHTML = '';

                                data.expiry_dates.forEach(expiry => {
                                    const badge = document.createElement('span');
                                    badge.classList.add('badge', 'bg-secondary', 'expiry-badge');
                                    badge.textContent = expiry;
                                    badge.dataset.expiry = expiry;

                                    badge.addEventListener('click', function() {
                                        // Toggle active state
                                        document.querySelectorAll('.expiry-badge').forEach(b => b.classList.remove('active'));
                                        badge.classList.add('active');

                                        // Filter data by expiry date
                                        selectedExpiry = expiry;
                                        renderOptionChain(filterByExpiry(data.data, expiry), data.underlying_value);
                                    });

                                    expiryDates.appendChild(badge);
                                });

                                expiryDatesContainer.classList.remove('d-none');

                                // Activate the first expiry date by default
                                if (data.expiry_dates.length > 0) {
                                    const firstBadge = expiryDates.querySelector('.expiry-badge');
                                    if (firstBadge) {
                                        firstBadge.click();
                                    }
                                }
                            } else {
                                // Render all data if no expiry dates
                                renderOptionChain(data.data, data.underlying_value);
                            }
                        } else {
                            showError(data.message || 'Failed to fetch option chain data');
                        }
                    })
                    .catch(error => {
                        hideLoading();
                        showError('Error fetching option chain data: ' + error.message);
                    });
            }

            function filterByExpiry(data, expiry) {
                if (!expiry) return data;
                return data.filter(item => item['Expiry Date'] === expiry);
            }

            function renderOptionChain(data, spotPrice) {
                optionChainBody.innerHTML = '';

                if (!data || data.length === 0) {
                    showError('No option chain data available');
                    return;
                }

                console.log("Rendering option chain with data:", data);
                console.log("Spot price:", spotPrice);

                // Sort by strike price
                data.sort((a, b) => a['Strike Price'] - b['Strike Price']);

                // Group data by strike price
                const strikeMap = {};
                data.forEach(option => {
                    const strikePrice = option['Strike Price'];
                    if (!strikeMap[strikePrice]) {
                        strikeMap[strikePrice] = {
                            strikePrice: strikePrice,
                            call: null,
                            put: null
                        };
                    }

                    if (option['Option Type'] === 'CE') {
                        strikeMap[strikePrice].call = option;
                    } else if (option['Option Type'] === 'PE') {
                        strikeMap[strikePrice].put = option;
                    }
                });

                // Convert back to array
                const groupedData = Object.values(strikeMap);
                console.log("Grouped data:", groupedData);

                // Find the closest strike price to the spot price (ATM)
                let closestStrike = groupedData.length > 0 ? groupedData[0].strikePrice : spotPrice;
                let minDiff = Math.abs(closestStrike - spotPrice);

                groupedData.forEach(option => {
                    const strikePrice = parseFloat(option.strikePrice);
                    const diff = Math.abs(strikePrice - spotPrice);
                    if (diff < minDiff) {
                        minDiff = diff;
                        closestStrike = strikePrice;
                    }
                });

                // Add header row
                const headerRow = document.createElement('tr');
                headerRow.innerHTML = `
                    <td colspan="5" class="text-center bg-primary text-white">CALLS</td>
                    <td class="text-center bg-dark text-white">STRIKE</td>
                    <td colspan="5" class="text-center bg-danger text-white">PUTS</td>
                `;
                optionChainBody.appendChild(headerRow);

                // Add column headers row
                const columnHeaderRow = document.createElement('tr');
                columnHeaderRow.innerHTML = `
                    <td>OI</td>
                    <td>CHNG IN OI</td>
                    <td>VOLUME</td>
                    <td>LTP</td>
                    <td>BID/ASK</td>
                    <td class="text-center">STRIKE</td>
                    <td>BID/ASK</td>
                    <td>LTP</td>
                    <td>VOLUME</td>
                    <td>CHNG IN OI</td>
                    <td>OI</td>
                `;
                optionChainBody.appendChild(columnHeaderRow);

                // Render each row
                groupedData.forEach(option => {
                    const row = document.createElement('tr');
                    const call = option.call || {};
                    const put = option.put || {};

                    // Determine if this is the ATM strike
                    const strikePrice = parseFloat(option.strikePrice);
                    const isATM = Math.abs(strikePrice - spotPrice) < 0.01 || strikePrice === closestStrike;

                    // Determine if call or put is in the money
                    const callInTheMoney = strikePrice < spotPrice;
                    const putInTheMoney = strikePrice > spotPrice;

                    // Call section
                    row.innerHTML += `
                        <td class="call-section ${callInTheMoney ? 'in-the-money' : ''}">${formatNumber(call['OI'] || 0)}</td>
                        <td class="call-section ${callInTheMoney ? 'in-the-money' : ''}">${formatNumber(call['Change in OI'] || 0)}</td>
                        <td class="call-section ${callInTheMoney ? 'in-the-money' : ''}">${formatNumber(call['Volume'] || 0)}</td>
                        <td class="call-section ${callInTheMoney ? 'in-the-money' : ''}">${formatDecimal(call['LTP'] || 0)}</td>
                        <td class="call-section ${callInTheMoney ? 'in-the-money' : ''}">${formatDecimal(call['Bid'] || 0)}/${formatDecimal(call['Ask'] || 0)}</td>
                    `;

                    // Strike price - highlight ATM strike in red
                    row.innerHTML += `<td class="text-center ${isATM ? 'atm-strike' : 'strike-price'}">${option.strikePrice}</td>`;

                    // Put section
                    row.innerHTML += `
                        <td class="put-section ${putInTheMoney ? 'in-the-money' : ''}">${formatDecimal(put['Bid'] || 0)}/${formatDecimal(put['Ask'] || 0)}</td>
                        <td class="put-section ${putInTheMoney ? 'in-the-money' : ''}">${formatDecimal(put['LTP'] || 0)}</td>
                        <td class="put-section ${putInTheMoney ? 'in-the-money' : ''}">${formatNumber(put['Volume'] || 0)}</td>
                        <td class="put-section ${putInTheMoney ? 'in-the-money' : ''}">${formatNumber(put['Change in OI'] || 0)}</td>
                        <td class="put-section ${putInTheMoney ? 'in-the-money' : ''}">${formatNumber(put['OI'] || 0)}</td>
                    `;

                    optionChainBody.appendChild(row);
                });

                // Scroll to the ATM strike
                setTimeout(() => {
                    const atmElement = document.querySelector('.atm-strike');
                    if (atmElement) {
                        atmElement.scrollIntoView({ block: 'center', behavior: 'smooth' });
                    }
                }, 100);

                optionChainContainer.classList.remove('d-none');
            }

            // Helper functions
            function formatNumber(num) {
                return new Intl.NumberFormat().format(num);
            }

            function formatDecimal(num) {
                return new Intl.NumberFormat(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(num);
            }

            function showLoading() {
                loadingIndicator.classList.remove('d-none');
            }

            function hideLoading() {
                loadingIndicator.classList.add('d-none');
            }

            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.classList.remove('d-none');
            }

            function hideError() {
                errorMessage.classList.add('d-none');
            }
        });
    </script>
</body>
</html>
