<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-Powered Options Analysis - Fyers Integration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .analysis-card {
            border-left: 4px solid #007bff;
            margin-bottom: 20px;
        }
        .ai-badge {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            margin-left: 10px;
        }
        .traditional-badge {
            background: #6c757d;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            margin-left: 10px;
        }
        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .loading-spinner {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .trade-recommendation {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .confidence-bar {
            height: 10px;
            background: #e9ecef;
            border-radius: 5px;
            overflow: hidden;
        }
        .confidence-fill {
            height: 100%;
            transition: width 0.3s ease;
        }
        .high-confidence { background: #28a745; }
        .medium-confidence { background: #ffc107; }
        .low-confidence { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="/">
                            <i class="fas fa-chart-line"></i> Fyers AI Options Analysis
                        </a>
                        <div class="navbar-nav ms-auto">
                            <a class="nav-link" href="/">Home</a>
                            <a class="nav-link" href="/options-analysis">Traditional Analysis</a>
                            <a class="nav-link active" href="/ai-options-analysis">AI Analysis</a>
                        </div>
                    </div>
                </nav>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-robot"></i> AI-Powered Options Analysis 
                            <span class="ai-badge">Gemini 2.0</span>
                        </h4>
                        <p class="mb-0">Advanced options analysis using Google's Gemini 2.0 Flash model with gamma acceleration strategy</p>
                    </div>
                    <div class="card-body">
                        <!-- Symbol Selection -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="symbolSelect" class="form-label">Select Symbol:</label>
                                <select class="form-select" id="symbolSelect">
                                    <option value="">Choose a symbol...</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="strikeCount" class="form-label">Strike Count:</label>
                                <select class="form-select" id="strikeCount">
                                    <option value="5">5 Strikes</option>
                                    <option value="10" selected>10 Strikes</option>
                                    <option value="15">15 Strikes</option>
                                    <option value="20">20 Strikes</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="analysisType" class="form-label">Analysis Type:</label>
                                <select class="form-select" id="analysisType">
                                    <option value="ai_only">AI Only</option>
                                    <option value="comparison" selected>AI + Traditional Comparison</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-primary w-100" onclick="fetchAndAnalyze()">
                                    <i class="fas fa-search"></i> Analyze
                                </button>
                            </div>
                        </div>

                        <!-- Loading Spinner -->
                        <div class="loading-spinner" id="loadingSpinner">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Fetching data and running AI analysis...</p>
                        </div>

                        <!-- Results Container -->
                        <div id="resultsContainer" style="display: none;">
                            <!-- Market Data Summary -->
                            <div class="card analysis-card">
                                <div class="card-header">
                                    <h5><i class="fas fa-chart-bar"></i> Market Data Summary</h5>
                                </div>
                                <div class="card-body" id="marketDataSummary">
                                    <!-- Market data will be populated here -->
                                </div>
                            </div>

                            <!-- Analysis Results -->
                            <div class="comparison-container" id="analysisResults">
                                <!-- AI and Traditional analysis results will be populated here -->
                            </div>

                            <!-- Trade Recommendations -->
                            <div class="card analysis-card">
                                <div class="card-header">
                                    <h5><i class="fas fa-bullseye"></i> AI Trade Recommendations</h5>
                                </div>
                                <div class="card-body" id="tradeRecommendations">
                                    <!-- Trade recommendations will be populated here -->
                                </div>
                            </div>
                        </div>

                        <!-- Error Container -->
                        <div id="errorContainer" style="display: none;">
                            <div class="alert alert-danger" role="alert">
                                <h4 class="alert-heading">Analysis Error</h4>
                                <p id="errorMessage"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let currentOptionsData = null;
        let currentUnderlyingValue = null;
        let currentSymbol = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadSymbols();
        });

        // Load available symbols
        function loadSymbols() {
            fetch('/api/fyers-stocks')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const select = document.getElementById('symbolSelect');
                        data.stocks.forEach(symbol => {
                            const option = document.createElement('option');
                            option.value = symbol;
                            option.textContent = symbol;
                            select.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading symbols:', error);
                });
        }

        // Main analysis function
        async function fetchAndAnalyze() {
            const symbol = document.getElementById('symbolSelect').value;
            const strikeCount = document.getElementById('strikeCount').value;
            const analysisType = document.getElementById('analysisType').value;

            if (!symbol) {
                alert('Please select a symbol');
                return;
            }

            // Show loading
            showLoading();
            hideResults();
            hideError();

            try {
                // Step 1: Fetch option chain data
                console.log(`Fetching option chain for ${symbol}...`);
                const optionChainResponse = await fetch(`/api/fyers-option-chain?symbol=${encodeURIComponent(symbol)}&strike_count=${strikeCount}`);
                const optionChainData = await optionChainResponse.json();

                if (!optionChainData.success) {
                    throw new Error(optionChainData.message || 'Failed to fetch option chain data');
                }

                // Store data globally
                currentOptionsData = optionChainData.data;
                currentUnderlyingValue = optionChainData.underlying_value;
                currentSymbol = symbol;

                // Step 2: Run AI analysis
                console.log('Running AI analysis...');
                const analysisResponse = await fetch('/api/ai-options-analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        options_data: currentOptionsData,
                        current_price: currentUnderlyingValue,
                        symbol: symbol,
                        days_to_expiry: null // Will be calculated from expiry date
                    })
                });

                const analysisData = await analysisResponse.json();

                if (!analysisData.success) {
                    throw new Error(analysisData.message || 'AI analysis failed');
                }

                // Display results
                displayResults(analysisData, optionChainData, analysisType);

            } catch (error) {
                console.error('Analysis error:', error);
                showError(error.message);
            } finally {
                hideLoading();
            }
        }

        // Display analysis results
        function displayResults(analysisData, optionChainData, analysisType) {
            // Show market data summary
            displayMarketDataSummary(optionChainData);

            // Show analysis results based on type
            if (analysisType === 'comparison') {
                displayComparisonResults(analysisData);
            } else {
                displayAIOnlyResults(analysisData.ai_analysis);
            }

            // Show trade recommendations
            displayTradeRecommendations(analysisData.ai_analysis);

            showResults();
        }

        // Display market data summary
        function displayMarketDataSummary(data) {
            const container = document.getElementById('marketDataSummary');
            container.innerHTML = `
                <div class="row">
                    <div class="col-md-3">
                        <h6>Symbol</h6>
                        <p class="h5 text-primary">${currentSymbol}</p>
                    </div>
                    <div class="col-md-3">
                        <h6>Current Price</h6>
                        <p class="h5 text-success">₹${data.underlying_value}</p>
                    </div>
                    <div class="col-md-3">
                        <h6>Options Count</h6>
                        <p class="h5">${data.data.length}</p>
                    </div>
                    <div class="col-md-3">
                        <h6>Analysis Time</h6>
                        <p class="h5">${data.timestamp}</p>
                    </div>
                </div>
                ${data.has_real_oi_data ? 
                    '<div class="alert alert-success"><i class="fas fa-check"></i> Real OI data available</div>' : 
                    '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> Limited OI data - results may be less accurate</div>'
                }
            `;
        }

        // Display comparison results (AI vs Traditional)
        function displayComparisonResults(data) {
            const container = document.getElementById('analysisResults');
            container.innerHTML = `
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-robot"></i> AI Analysis <span class="ai-badge">Gemini 2.0</span></h5>
                    </div>
                    <div class="card-body">
                        ${formatAIAnalysis(data.ai_analysis)}
                    </div>
                </div>
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-calculator"></i> Traditional Analysis <span class="traditional-badge">Mathematical</span></h5>
                    </div>
                    <div class="card-body">
                        ${formatTraditionalAnalysis(data.traditional_analysis)}
                    </div>
                </div>
            `;
        }

        // Format AI analysis results
        function formatAIAnalysis(aiData) {
            if (!aiData.success) {
                return `<div class="alert alert-danger">AI Analysis Failed: ${aiData.error || 'Unknown error'}</div>`;
            }

            let html = '';

            // Overall bias and confidence
            if (aiData.overall_bias && aiData.confidence_level) {
                const confidenceClass = aiData.confidence_level > 70 ? 'high-confidence' : 
                                      aiData.confidence_level > 40 ? 'medium-confidence' : 'low-confidence';
                
                html += `
                    <div class="mb-3">
                        <h6>Overall Market Bias</h6>
                        <p class="h5 text-primary">${aiData.overall_bias}</p>
                        <div class="confidence-bar">
                            <div class="confidence-fill ${confidenceClass}" style="width: ${aiData.confidence_level}%"></div>
                        </div>
                        <small>Confidence: ${aiData.confidence_level}%</small>
                    </div>
                `;
            }

            // Key insights
            if (aiData.key_insights && aiData.key_insights.length > 0) {
                html += `
                    <div class="mb-3">
                        <h6>Key AI Insights</h6>
                        <ul>
                            ${aiData.key_insights.map(insight => `<li>${insight}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }

            // If we have structured analysis
            if (aiData.max_pain_analysis) {
                html += `<h6>Max Pain Analysis</h6><p>${aiData.max_pain_analysis.analysis || aiData.max_pain_analysis.current_vs_max_pain || 'Analysis not available'}</p>`;
            }

            if (aiData.pcr_analysis) {
                html += `<h6>Put-Call Ratio Analysis</h6><p>${aiData.pcr_analysis.analysis || aiData.pcr_analysis.interpretation || 'Analysis not available'}</p>`;
            }

            // If we have raw AI text
            if (aiData.ai_analysis_text) {
                html += `
                    <div class="mb-3">
                        <h6>Detailed AI Analysis</h6>
                        <div style="white-space: pre-wrap; background: #f8f9fa; padding: 15px; border-radius: 5px;">
                            ${aiData.ai_analysis_text}
                        </div>
                    </div>
                `;
            }

            return html || '<p>AI analysis data not available in expected format.</p>';
        }

        // Format traditional analysis results
        function formatTraditionalAnalysis(tradData) {
            if (!tradData) {
                return '<p>Traditional analysis not available.</p>';
            }

            return `
                <div class="mb-3">
                    <h6>Overall Bias</h6>
                    <p class="h5 text-info">${tradData.overall_bias}</p>
                    <small>Confidence: ${tradData.confidence}%</small>
                </div>
                <div class="mb-3">
                    <h6>Max Pain</h6>
                    <p>Strike: ₹${tradData.max_pain?.max_pain_strike || 'N/A'}</p>
                </div>
                <div class="mb-3">
                    <h6>Put-Call Ratio</h6>
                    <p>PCR (OI): ${tradData.pcr?.pcr_oi?.toFixed(2) || 'N/A'}</p>
                    <p>PCR (Volume): ${tradData.pcr?.pcr_volume?.toFixed(2) || 'N/A'}</p>
                </div>
                <div class="mb-3">
                    <h6>IV Percentile</h6>
                    <p>${tradData.iv_percentile?.iv_percentile?.toFixed(1) || 'N/A'}%</p>
                </div>
            `;
        }

        // Display trade recommendations
        function displayTradeRecommendations(aiData) {
            const container = document.getElementById('tradeRecommendations');
            
            if (!aiData.success || !aiData.trade_recommendations || aiData.trade_recommendations.length === 0) {
                container.innerHTML = '<p>No trade recommendations available from AI analysis.</p>';
                return;
            }

            let html = '';
            aiData.trade_recommendations.forEach((trade, index) => {
                html += `
                    <div class="trade-recommendation">
                        <h6><i class="fas fa-chart-line"></i> Trade ${index + 1}: ${trade.type} Option</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Strike:</strong> ₹${trade.strike}
                            </div>
                            <div class="col-md-3">
                                <strong>Entry:</strong> ₹${trade.entry_price}
                            </div>
                            <div class="col-md-3">
                                <strong>Target:</strong> ₹${trade.target}
                            </div>
                            <div class="col-md-3">
                                <strong>Stop Loss:</strong> ₹${trade.stop_loss}
                            </div>
                        </div>
                        <div class="mt-2">
                            <strong>Risk-Reward:</strong> ${trade.risk_reward}
                        </div>
                        <div class="mt-2">
                            <strong>Rationale:</strong> ${trade.rationale}
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // Utility functions
        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'block';
        }

        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
        }

        function showResults() {
            document.getElementById('resultsContainer').style.display = 'block';
        }

        function hideResults() {
            document.getElementById('resultsContainer').style.display = 'none';
        }

        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorContainer').style.display = 'block';
        }

        function hideError() {
            document.getElementById('errorContainer').style.display = 'none';
        }
    </script>
</body>
</html>
