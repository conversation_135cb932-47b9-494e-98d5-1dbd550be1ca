#!/usr/bin/env python3
"""
Live Options Calculator for Real-time PCR, Greeks, and Market Analysis
"""

import math
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Tuple
from scipy.stats import norm


class LiveOptionsCalculator:
    """
    Real-time options analysis with live PCR, Greeks, and market calculations
    """
    
    def __init__(self):
        self.risk_free_rate = 0.065  # 6.5% risk-free rate (adjust as needed)
    
    def calculate_comprehensive_analysis(self, options_data: List[Dict], current_price: float, 
                                       days_to_expiry: int = None) -> Dict[str, Any]:
        """
        Calculate comprehensive live options analysis
        
        Args:
            options_data: Real option chain data from Fyers API
            current_price: Current underlying price
            days_to_expiry: Days to expiration
            
        Returns:
            Dict containing all calculated metrics
        """
        try:
            # Separate calls and puts
            calls = [opt for opt in options_data if opt.get('Option Type') == 'CE']
            puts = [opt for opt in options_data if opt.get('Option Type') == 'PE']
            
            # Calculate basic metrics
            pcr_metrics = self.calculate_live_pcr(calls, puts)
            max_pain = self.calculate_max_pain(options_data)
            oi_analysis = self.calculate_oi_analysis(calls, puts, current_price)
            volume_analysis = self.calculate_volume_analysis(calls, puts)
            
            # Calculate Greeks if days to expiry is available
            greeks_analysis = {}
            if days_to_expiry:
                greeks_analysis = self.calculate_live_greeks(options_data, current_price, days_to_expiry)
            
            # Find key support/resistance levels
            key_levels = self.find_key_levels(options_data, current_price)
            
            # Generate trade recommendations based on real data
            trade_recommendations = self.generate_live_trade_recommendations(
                options_data, current_price, pcr_metrics, max_pain, key_levels
            )
            
            # Calculate market sentiment
            market_sentiment = self.calculate_market_sentiment(
                pcr_metrics, max_pain, oi_analysis, current_price
            )
            
            return {
                'success': True,
                'timestamp': datetime.now().isoformat(),
                'current_price': current_price,
                'pcr_analysis': pcr_metrics,
                'max_pain_analysis': max_pain,
                'oi_analysis': oi_analysis,
                'volume_analysis': volume_analysis,
                'greeks_analysis': greeks_analysis,
                'key_levels': key_levels,
                'trade_recommendations': trade_recommendations,
                'market_sentiment': market_sentiment,
                'data_quality': self.assess_data_quality(options_data)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Analysis failed: {str(e)}",
                'timestamp': datetime.now().isoformat()
            }
    
    def calculate_live_pcr(self, calls: List[Dict], puts: List[Dict]) -> Dict[str, Any]:
        """Calculate real-time Put-Call Ratio"""
        try:
            # Calculate OI-based PCR
            total_call_oi = sum(opt.get('OI', 0) for opt in calls)
            total_put_oi = sum(opt.get('OI', 0) for opt in puts)
            pcr_oi = total_put_oi / total_call_oi if total_call_oi > 0 else 0
            
            # Calculate Volume-based PCR
            total_call_volume = sum(opt.get('Volume', 0) for opt in calls)
            total_put_volume = sum(opt.get('Volume', 0) for opt in puts)
            pcr_volume = total_put_volume / total_call_volume if total_call_volume > 0 else 0
            
            # Calculate Value-based PCR (OI * LTP)
            call_value = sum(opt.get('OI', 0) * opt.get('LTP', 0) for opt in calls)
            put_value = sum(opt.get('OI', 0) * opt.get('LTP', 0) for opt in puts)
            pcr_value = put_value / call_value if call_value > 0 else 0
            
            # Interpret PCR
            interpretation = self.interpret_pcr(pcr_oi)
            
            return {
                'pcr_oi': round(pcr_oi, 3),
                'pcr_volume': round(pcr_volume, 3),
                'pcr_value': round(pcr_value, 3),
                'total_call_oi': total_call_oi,
                'total_put_oi': total_put_oi,
                'total_call_volume': total_call_volume,
                'total_put_volume': total_put_volume,
                'interpretation': interpretation,
                'signal_strength': self.get_pcr_signal_strength(pcr_oi)
            }
            
        except Exception as e:
            return {'error': f"PCR calculation failed: {str(e)}"}
    
    def calculate_max_pain(self, options_data: List[Dict]) -> Dict[str, Any]:
        """Calculate Max Pain point from real option data"""
        try:
            # Group by strike price
            strikes = {}
            for opt in options_data:
                strike = opt.get('Strike Price', 0)
                if strike == 0:
                    continue
                    
                if strike not in strikes:
                    strikes[strike] = {'call_oi': 0, 'put_oi': 0}
                
                if opt.get('Option Type') == 'CE':
                    strikes[strike]['call_oi'] = opt.get('OI', 0)
                elif opt.get('Option Type') == 'PE':
                    strikes[strike]['put_oi'] = opt.get('OI', 0)
            
            # Calculate pain for each strike
            max_pain_data = []
            for strike, data in strikes.items():
                call_pain = sum(max(0, strike - s) * strikes[s]['call_oi'] 
                              for s in strikes.keys() if s < strike)
                put_pain = sum(max(0, s - strike) * strikes[s]['put_oi'] 
                             for s in strikes.keys() if s > strike)
                total_pain = call_pain + put_pain
                
                max_pain_data.append({
                    'strike': strike,
                    'total_pain': total_pain,
                    'call_pain': call_pain,
                    'put_pain': put_pain
                })
            
            # Find max pain point
            if max_pain_data:
                max_pain_point = min(max_pain_data, key=lambda x: x['total_pain'])
                
                return {
                    'max_pain_strike': max_pain_point['strike'],
                    'total_pain': max_pain_point['total_pain'],
                    'all_strikes_pain': max_pain_data,
                    'pain_range': {
                        'min_pain': min(x['total_pain'] for x in max_pain_data),
                        'max_pain': max(x['total_pain'] for x in max_pain_data)
                    }
                }
            else:
                return {'error': 'No valid strike data for max pain calculation'}
                
        except Exception as e:
            return {'error': f"Max pain calculation failed: {str(e)}"}
    
    def calculate_oi_analysis(self, calls: List[Dict], puts: List[Dict], current_price: float) -> Dict[str, Any]:
        """Analyze Open Interest distribution"""
        try:
            # Find highest OI strikes
            call_oi_strikes = sorted(calls, key=lambda x: x.get('OI', 0), reverse=True)[:5]
            put_oi_strikes = sorted(puts, key=lambda x: x.get('OI', 0), reverse=True)[:5]
            
            # Analyze OI concentration
            total_call_oi = sum(opt.get('OI', 0) for opt in calls)
            total_put_oi = sum(opt.get('OI', 0) for opt in puts)
            
            # Top 5 strikes concentration
            top5_call_oi = sum(opt.get('OI', 0) for opt in call_oi_strikes)
            top5_put_oi = sum(opt.get('OI', 0) for opt in put_oi_strikes)
            
            call_concentration = (top5_call_oi / total_call_oi * 100) if total_call_oi > 0 else 0
            put_concentration = (top5_put_oi / total_put_oi * 100) if total_put_oi > 0 else 0
            
            # Find support and resistance from OI
            resistance_levels = [opt.get('Strike Price', 0) for opt in call_oi_strikes 
                               if opt.get('Strike Price', 0) > current_price][:3]
            support_levels = [opt.get('Strike Price', 0) for opt in put_oi_strikes 
                            if opt.get('Strike Price', 0) < current_price][:3]
            
            return {
                'highest_call_oi_strikes': [
                    {'strike': opt.get('Strike Price'), 'oi': opt.get('OI'), 'ltp': opt.get('LTP')}
                    for opt in call_oi_strikes
                ],
                'highest_put_oi_strikes': [
                    {'strike': opt.get('Strike Price'), 'oi': opt.get('OI'), 'ltp': opt.get('LTP')}
                    for opt in put_oi_strikes
                ],
                'oi_concentration': {
                    'call_concentration_pct': round(call_concentration, 2),
                    'put_concentration_pct': round(put_concentration, 2)
                },
                'key_levels': {
                    'resistance_levels': resistance_levels,
                    'support_levels': support_levels
                }
            }
            
        except Exception as e:
            return {'error': f"OI analysis failed: {str(e)}"}
    
    def calculate_volume_analysis(self, calls: List[Dict], puts: List[Dict]) -> Dict[str, Any]:
        """Analyze trading volume patterns"""
        try:
            # Sort by volume
            high_volume_calls = sorted(calls, key=lambda x: x.get('Volume', 0), reverse=True)[:5]
            high_volume_puts = sorted(puts, key=lambda x: x.get('Volume', 0), reverse=True)[:5]
            
            # Calculate volume metrics
            total_call_volume = sum(opt.get('Volume', 0) for opt in calls)
            total_put_volume = sum(opt.get('Volume', 0) for opt in puts)
            
            # Volume to OI ratio (activity indicator)
            volume_oi_ratios = []
            for opt in calls + puts:
                volume = opt.get('Volume', 0)
                oi = opt.get('OI', 0)
                if oi > 0:
                    ratio = volume / oi
                    volume_oi_ratios.append({
                        'strike': opt.get('Strike Price'),
                        'type': opt.get('Option Type'),
                        'volume_oi_ratio': round(ratio, 3),
                        'volume': volume,
                        'oi': oi
                    })
            
            # Sort by highest activity
            high_activity = sorted(volume_oi_ratios, key=lambda x: x['volume_oi_ratio'], reverse=True)[:10]
            
            return {
                'high_volume_calls': [
                    {'strike': opt.get('Strike Price'), 'volume': opt.get('Volume'), 'ltp': opt.get('LTP')}
                    for opt in high_volume_calls
                ],
                'high_volume_puts': [
                    {'strike': opt.get('Strike Price'), 'volume': opt.get('Volume'), 'ltp': opt.get('LTP')}
                    for opt in high_volume_puts
                ],
                'total_volumes': {
                    'call_volume': total_call_volume,
                    'put_volume': total_put_volume
                },
                'high_activity_strikes': high_activity
            }
            
        except Exception as e:
            return {'error': f"Volume analysis failed: {str(e)}"}

    def calculate_live_greeks(self, options_data: List[Dict], current_price: float,
                            days_to_expiry: int) -> Dict[str, Any]:
        """Calculate live Greeks using Black-Scholes model"""
        try:
            time_to_expiry = days_to_expiry / 365.0
            if time_to_expiry <= 0:
                time_to_expiry = 1/365  # Minimum 1 day

            greeks_data = []
            total_gamma_exposure = 0

            for opt in options_data:
                strike = opt.get('Strike Price', 0)
                ltp = opt.get('LTP', 0)
                oi = opt.get('OI', 0)
                option_type = opt.get('Option Type', '')

                if strike == 0 or ltp == 0:
                    continue

                # Estimate implied volatility from LTP
                iv = self.estimate_iv_from_price(current_price, strike, time_to_expiry, ltp, option_type)

                # Calculate Greeks
                greeks = self.calculate_black_scholes_greeks(
                    current_price, strike, time_to_expiry, self.risk_free_rate, iv, option_type
                )

                # Calculate gamma exposure
                gamma_exposure = greeks['gamma'] * oi * current_price * current_price * 0.01
                total_gamma_exposure += gamma_exposure

                greeks_data.append({
                    'strike': strike,
                    'option_type': option_type,
                    'oi': oi,
                    'ltp': ltp,
                    'iv': round(iv, 4),
                    'delta': round(greeks['delta'], 4),
                    'gamma': round(greeks['gamma'], 6),
                    'theta': round(greeks['theta'], 4),
                    'vega': round(greeks['vega'], 4),
                    'gamma_exposure': round(gamma_exposure, 2)
                })

            # Find highest gamma strikes
            high_gamma_strikes = sorted(greeks_data, key=lambda x: abs(x['gamma']), reverse=True)[:5]

            return {
                'total_gamma_exposure': round(total_gamma_exposure, 2),
                'high_gamma_strikes': high_gamma_strikes,
                'gamma_exposure_interpretation': self.interpret_gamma_exposure(total_gamma_exposure),
                'all_greeks': greeks_data
            }

        except Exception as e:
            return {'error': f"Greeks calculation failed: {str(e)}"}

    def calculate_black_scholes_greeks(self, S: float, K: float, T: float, r: float,
                                     sigma: float, option_type: str) -> Dict[str, float]:
        """Calculate Black-Scholes Greeks"""
        try:
            if T <= 0 or sigma <= 0:
                return {'delta': 0, 'gamma': 0, 'theta': 0, 'vega': 0}

            d1 = (math.log(S/K) + (r + 0.5*sigma**2)*T) / (sigma*math.sqrt(T))
            d2 = d1 - sigma*math.sqrt(T)

            if option_type == 'CE':  # Call
                delta = norm.cdf(d1)
                theta = (-S*norm.pdf(d1)*sigma/(2*math.sqrt(T)) - r*K*math.exp(-r*T)*norm.cdf(d2)) / 365
            else:  # Put
                delta = norm.cdf(d1) - 1
                theta = (-S*norm.pdf(d1)*sigma/(2*math.sqrt(T)) + r*K*math.exp(-r*T)*norm.cdf(-d2)) / 365

            gamma = norm.pdf(d1) / (S*sigma*math.sqrt(T))
            vega = S*norm.pdf(d1)*math.sqrt(T) / 100

            return {
                'delta': delta,
                'gamma': gamma,
                'theta': theta,
                'vega': vega
            }

        except Exception:
            return {'delta': 0, 'gamma': 0, 'theta': 0, 'vega': 0}

    def estimate_iv_from_price(self, S: float, K: float, T: float, market_price: float,
                              option_type: str) -> float:
        """Estimate implied volatility from market price using Newton-Raphson"""
        try:
            if T <= 0 or market_price <= 0:
                return 0.2  # Default 20% volatility

            # Initial guess
            iv = 0.2

            for _ in range(10):  # Max 10 iterations
                d1 = (math.log(S/K) + (self.risk_free_rate + 0.5*iv**2)*T) / (iv*math.sqrt(T))
                d2 = d1 - iv*math.sqrt(T)

                if option_type == 'CE':
                    price = S*norm.cdf(d1) - K*math.exp(-self.risk_free_rate*T)*norm.cdf(d2)
                else:
                    price = K*math.exp(-self.risk_free_rate*T)*norm.cdf(-d2) - S*norm.cdf(-d1)

                vega = S*norm.pdf(d1)*math.sqrt(T)

                if abs(vega) < 1e-6:
                    break

                iv = iv - (price - market_price) / vega

                if iv <= 0:
                    iv = 0.01
                elif iv > 5:
                    iv = 5

            return max(0.01, min(5.0, iv))

        except Exception:
            return 0.2

    def find_key_levels(self, options_data: List[Dict], current_price: float) -> Dict[str, Any]:
        """Find key support and resistance levels from option data"""
        try:
            # Group by strike and sum OI
            strike_oi = {}
            for opt in options_data:
                strike = opt.get('Strike Price', 0)
                oi = opt.get('OI', 0)

                if strike == 0:
                    continue

                if strike not in strike_oi:
                    strike_oi[strike] = 0
                strike_oi[strike] += oi

            # Sort by total OI
            sorted_strikes = sorted(strike_oi.items(), key=lambda x: x[1], reverse=True)

            # Find support and resistance
            resistance_levels = [strike for strike, oi in sorted_strikes if strike > current_price][:5]
            support_levels = [strike for strike, oi in sorted_strikes if strike < current_price][:5]

            # Find ATM strike
            atm_strike = min(strike_oi.keys(), key=lambda x: abs(x - current_price))

            return {
                'atm_strike': atm_strike,
                'immediate_resistance': resistance_levels[0] if resistance_levels else None,
                'immediate_support': support_levels[0] if support_levels else None,
                'all_resistance_levels': resistance_levels,
                'all_support_levels': support_levels,
                'highest_oi_strikes': [{'strike': strike, 'total_oi': oi} for strike, oi in sorted_strikes[:10]]
            }

        except Exception as e:
            return {'error': f"Key levels calculation failed: {str(e)}"}

    def generate_live_trade_recommendations(self, options_data: List[Dict], current_price: float,
                                          pcr_metrics: Dict, max_pain: Dict, key_levels: Dict) -> List[Dict]:
        """Generate trade recommendations based on real option data"""
        try:
            recommendations = []

            # Get real strikes from data
            calls = [opt for opt in options_data if opt.get('Option Type') == 'CE']
            puts = [opt for opt in options_data if opt.get('Option Type') == 'PE']

            # Strategy 1: Based on PCR analysis
            if pcr_metrics.get('pcr_oi', 0) < 0.7:  # Bullish
                # Find OTM calls with good liquidity
                otm_calls = [opt for opt in calls
                           if opt.get('Strike Price', 0) > current_price * 1.01
                           and opt.get('Strike Price', 0) < current_price * 1.05
                           and opt.get('OI', 0) > 1000]

                if otm_calls:
                    best_call = max(otm_calls, key=lambda x: x.get('OI', 0))
                    entry_price = best_call.get('LTP', 0)

                    recommendations.append({
                        'type': 'CE',
                        'strike': best_call.get('Strike Price'),
                        'entry_price': entry_price,
                        'target': round(entry_price * 1.5, 2),
                        'stop_loss': round(entry_price * 0.7, 2),
                        'risk_reward': '1:1.5',
                        'rationale': f"Bullish PCR ({pcr_metrics.get('pcr_oi'):.2f}) suggests upward movement. High OI strike with good liquidity.",
                        'confidence': 'High' if pcr_metrics.get('pcr_oi', 0) < 0.6 else 'Medium'
                    })

            elif pcr_metrics.get('pcr_oi', 0) > 1.3:  # Bearish
                # Find OTM puts with good liquidity
                otm_puts = [opt for opt in puts
                          if opt.get('Strike Price', 0) < current_price * 0.99
                          and opt.get('Strike Price', 0) > current_price * 0.95
                          and opt.get('OI', 0) > 1000]

                if otm_puts:
                    best_put = max(otm_puts, key=lambda x: x.get('OI', 0))
                    entry_price = best_put.get('LTP', 0)

                    recommendations.append({
                        'type': 'PE',
                        'strike': best_put.get('Strike Price'),
                        'entry_price': entry_price,
                        'target': round(entry_price * 1.5, 2),
                        'stop_loss': round(entry_price * 0.7, 2),
                        'risk_reward': '1:1.5',
                        'rationale': f"Bearish PCR ({pcr_metrics.get('pcr_oi'):.2f}) suggests downward movement. High OI strike with good liquidity.",
                        'confidence': 'High' if pcr_metrics.get('pcr_oi', 0) > 1.5 else 'Medium'
                    })

            # Strategy 2: Max Pain based strategy
            max_pain_strike = max_pain.get('max_pain_strike', 0)
            if max_pain_strike and abs(max_pain_strike - current_price) > current_price * 0.02:
                if max_pain_strike > current_price:
                    # Price likely to move towards max pain (upward)
                    suitable_calls = [opt for opt in calls
                                    if opt.get('Strike Price', 0) <= max_pain_strike
                                    and opt.get('Strike Price', 0) > current_price
                                    and opt.get('OI', 0) > 500]

                    if suitable_calls:
                        best_call = min(suitable_calls, key=lambda x: abs(x.get('Strike Price', 0) - (current_price + max_pain_strike)/2))
                        entry_price = best_call.get('LTP', 0)

                        recommendations.append({
                            'type': 'CE',
                            'strike': best_call.get('Strike Price'),
                            'entry_price': entry_price,
                            'target': round(entry_price * 2, 2),
                            'stop_loss': round(entry_price * 0.6, 2),
                            'risk_reward': '1:2',
                            'rationale': f"Max Pain at {max_pain_strike} suggests upward movement from current {current_price}. Targeting price convergence.",
                            'confidence': 'Medium'
                        })

            # Limit to top 3 recommendations
            return recommendations[:3]

        except Exception as e:
            return [{'error': f"Trade recommendation generation failed: {str(e)}"}]

    def calculate_market_sentiment(self, pcr_metrics: Dict, max_pain: Dict,
                                 oi_analysis: Dict, current_price: float) -> Dict[str, Any]:
        """Calculate overall market sentiment"""
        try:
            sentiment_score = 0
            signals = []

            # PCR signal
            pcr_oi = pcr_metrics.get('pcr_oi', 1)
            if pcr_oi < 0.7:
                sentiment_score += 2
                signals.append("Bullish PCR")
            elif pcr_oi > 1.3:
                sentiment_score -= 2
                signals.append("Bearish PCR")

            # Max Pain signal
            max_pain_strike = max_pain.get('max_pain_strike', current_price)
            if max_pain_strike > current_price * 1.01:
                sentiment_score += 1
                signals.append("Max Pain above current price")
            elif max_pain_strike < current_price * 0.99:
                sentiment_score -= 1
                signals.append("Max Pain below current price")

            # OI concentration signal
            call_concentration = oi_analysis.get('oi_concentration', {}).get('call_concentration_pct', 50)
            put_concentration = oi_analysis.get('oi_concentration', {}).get('put_concentration_pct', 50)

            if call_concentration > put_concentration + 10:
                sentiment_score += 1
                signals.append("Higher call OI concentration")
            elif put_concentration > call_concentration + 10:
                sentiment_score -= 1
                signals.append("Higher put OI concentration")

            # Determine overall sentiment
            if sentiment_score >= 2:
                overall_sentiment = "Bullish"
                confidence = min(90, 60 + sentiment_score * 10)
            elif sentiment_score <= -2:
                overall_sentiment = "Bearish"
                confidence = min(90, 60 + abs(sentiment_score) * 10)
            else:
                overall_sentiment = "Neutral"
                confidence = 50

            return {
                'overall_sentiment': overall_sentiment,
                'confidence_level': confidence,
                'sentiment_score': sentiment_score,
                'contributing_signals': signals,
                'interpretation': self.interpret_sentiment(overall_sentiment, confidence)
            }

        except Exception as e:
            return {'error': f"Sentiment calculation failed: {str(e)}"}

    def assess_data_quality(self, options_data: List[Dict]) -> Dict[str, Any]:
        """Assess the quality of option chain data"""
        try:
            total_options = len(options_data)
            options_with_oi = len([opt for opt in options_data if opt.get('OI', 0) > 0])
            options_with_volume = len([opt for opt in options_data if opt.get('Volume', 0) > 0])
            options_with_ltp = len([opt for opt in options_data if opt.get('LTP', 0) > 0])

            quality_score = (
                (options_with_oi / total_options * 40) +
                (options_with_volume / total_options * 30) +
                (options_with_ltp / total_options * 30)
            ) if total_options > 0 else 0

            return {
                'total_options': total_options,
                'options_with_oi': options_with_oi,
                'options_with_volume': options_with_volume,
                'options_with_ltp': options_with_ltp,
                'quality_score': round(quality_score, 2),
                'quality_rating': 'High' if quality_score > 80 else 'Medium' if quality_score > 60 else 'Low'
            }

        except Exception as e:
            return {'error': f"Data quality assessment failed: {str(e)}"}

    # Helper methods for interpretations
    def interpret_pcr(self, pcr_oi: float) -> str:
        """Interpret PCR value"""
        if pcr_oi < 0.7:
            return "Bullish - Low put activity suggests optimism"
        elif pcr_oi > 1.3:
            return "Bearish - High put activity suggests pessimism"
        else:
            return "Neutral - Balanced put-call activity"

    def get_pcr_signal_strength(self, pcr_oi: float) -> str:
        """Get PCR signal strength"""
        if pcr_oi < 0.5 or pcr_oi > 1.8:
            return "Strong"
        elif pcr_oi < 0.7 or pcr_oi > 1.3:
            return "Medium"
        else:
            return "Weak"

    def interpret_gamma_exposure(self, gamma_exposure: float) -> str:
        """Interpret gamma exposure"""
        if abs(gamma_exposure) > 1000000:
            return "High gamma exposure - Expect volatile price movements"
        elif abs(gamma_exposure) > 500000:
            return "Medium gamma exposure - Moderate volatility expected"
        else:
            return "Low gamma exposure - Relatively stable price action"

    def interpret_sentiment(self, sentiment: str, confidence: int) -> str:
        """Interpret overall market sentiment"""
        return f"{sentiment} sentiment with {confidence}% confidence. " + \
               ("Strong conviction" if confidence > 75 else "Moderate conviction" if confidence > 60 else "Low conviction")
