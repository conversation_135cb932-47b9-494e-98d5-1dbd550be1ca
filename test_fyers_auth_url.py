#!/usr/bin/env python3
"""
Test different Fyers authorization URLs to find the correct one
"""

import requests
import os
from dotenv import load_dotenv

load_dotenv()

def test_auth_urls():
    """Test different Fyers authorization URL formats"""
    
    app_id = os.environ.get('FYERS_APP_ID', 'B5QJCSZE9E-100')
    redirect_uri = "https://trade.fyers.in/api-login/redirect-to-app"
    
    # Different URL formats to test
    urls_to_test = [
        f"https://api.fyers.in/api/v2/generate-authcode?client_id={app_id}&redirect_uri={redirect_uri}&response_type=code&state=sample_state",
        f"https://api.fyers.in/api/v3/generate-authcode?client_id={app_id}&redirect_uri={redirect_uri}&response_type=code&state=sample_state",
        f"https://api-t1.fyers.in/api/v2/generate-authcode?client_id={app_id}&redirect_uri={redirect_uri}&response_type=code&state=sample_state",
        f"https://api-t1.fyers.in/api/v3/generate-authcode?client_id={app_id}&redirect_uri={redirect_uri}&response_type=code&state=sample_state",
        f"https://api-t2.fyers.in/api/v3/generate-authcode?client_id={app_id}&redirect_uri={redirect_uri}&response_type=code&state=sample_state"
    ]
    
    print("🔍 Testing Fyers Authorization URLs")
    print("=" * 50)
    
    for i, url in enumerate(urls_to_test, 1):
        print(f"\n{i}. Testing: {url}")
        
        try:
            # Make a HEAD request to check if the URL is accessible
            response = requests.head(url, timeout=10, allow_redirects=True)
            print(f"   Status Code: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ URL appears to be working")
            elif response.status_code in [301, 302, 307, 308]:
                print(f"   🔄 Redirect to: {response.headers.get('Location', 'Unknown')}")
            elif response.status_code == 404:
                print("   ❌ URL not found")
            elif response.status_code == 403:
                print("   ⚠️  Access forbidden (might still work for auth)")
            else:
                print(f"   ⚠️  Unexpected status: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print("   ⏰ Request timed out")
        except requests.exceptions.ConnectionError:
            print("   ❌ Connection error")
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("📋 RECOMMENDATION:")
    print("Try the URLs marked with ✅ or 🔄 first.")
    print("If none work, check the official Fyers API documentation.")

def test_fyers_session_model():
    """Test creating a Fyers session model to see what URL it uses"""
    try:
        from fyers_apiv3 import fyersModel
        
        app_id = os.environ.get('FYERS_APP_ID')
        secret_id = os.environ.get('FYERS_SECRET_ID')
        
        if not app_id or not secret_id:
            print("❌ Missing credentials")
            return
        
        print("\n🔧 Testing Fyers SessionModel...")
        
        session = fyersModel.SessionModel(
            client_id=app_id,
            secret_key=secret_id,
            redirect_uri="https://trade.fyers.in/api-login/redirect-to-app",
            response_type="code",
            grant_type="authorization_code"
        )
        
        # Try to get the auth URL from the session model
        try:
            auth_url = session.generate_authcode()
            print(f"✅ SessionModel auth URL: {auth_url}")
        except Exception as e:
            print(f"❌ Error getting auth URL from SessionModel: {str(e)}")
            
    except ImportError:
        print("❌ fyers_apiv3 not available")
    except Exception as e:
        print(f"❌ Error testing SessionModel: {str(e)}")

if __name__ == "__main__":
    test_auth_urls()
    test_fyers_session_model()
