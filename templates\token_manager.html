<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fyers Token Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            padding-top: 20px;
            padding-bottom: 40px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            font-weight: bold;
        }
        .token-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .token-value {
            font-family: monospace;
            word-break: break-all;
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin-top: 5px;
        }
        .alert {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark mb-4">
        <div class="container">
            <a class="navbar-brand" href="/">Options Analysis</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/options-analysis">Option Chain</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analyze-all">Analyze All</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/token-manager">Token Manager</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        <h1 class="mb-4">Fyers Token Manager</h1>

        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Manage Access Token</h5>
            </div>
            <div class="card-body">
                {% if token_exists and token_data %}
                <div class="token-info">
                    <h5>Current Token Information</h5>
                    <p><strong>Last Updated:</strong> {{ token_data.timestamp_readable }}</p>
                    <p><strong>Access Token:</strong></p>
                    <div class="token-value">{{ token_data.access_token }}</div>
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    No access token found. Please add your Fyers API access token below.
                </div>
                {% endif %}

                <h5 class="mt-4">Update Access Token</h5>
                <form id="tokenForm">
                    <div class="mb-3">
                        <label for="accessToken" class="form-label">Access Token</label>
                        <textarea class="form-control" id="accessToken" rows="3" placeholder="Paste your Fyers API access token here" required></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">Update Token</button>
                </form>

                <div class="alert alert-success mt-3 d-none" id="successAlert">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    Token updated successfully!
                </div>
                <div class="alert alert-danger mt-3 d-none" id="errorAlert">
                    <i class="bi bi-x-circle-fill me-2"></i>
                    <span id="errorMessage">Error updating token.</span>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">How to Get Your Fyers API Access Token</h5>
            </div>
            <div class="card-body">
                <ol class="mb-0">
                    <li class="mb-2">Log in to your <a href="https://fyers.in/" target="_blank">Fyers account</a>.</li>
                    <li class="mb-2">Navigate to the API section in your dashboard.</li>
                    <li class="mb-2">Generate a new access token using your app credentials.</li>
                    <li class="mb-2">Copy the access token and paste it in the form above.</li>
                    <li>Click "Update Token" to save your token for use in this application.</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const tokenForm = document.getElementById('tokenForm');
            const successAlert = document.getElementById('successAlert');
            const errorAlert = document.getElementById('errorAlert');
            const errorMessage = document.getElementById('errorMessage');

            tokenForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const accessToken = document.getElementById('accessToken').value.trim();

                if (!accessToken) {
                    showError('Access token cannot be empty');
                    return;
                }

                // Hide any existing alerts
                successAlert.classList.add('d-none');
                errorAlert.classList.add('d-none');

                // Send the token to the server
                fetch('/update-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `access_token=${encodeURIComponent(accessToken)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSuccess();
                        // Reload the page after a short delay to show the updated token
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        showError(data.message || 'Error updating token');
                    }
                })
                .catch(error => {
                    showError('Network error: ' + error.message);
                });
            });

            function showSuccess() {
                successAlert.classList.remove('d-none');
                errorAlert.classList.add('d-none');
            }

            function showError(message) {
                errorMessage.textContent = message;
                errorAlert.classList.remove('d-none');
                successAlert.classList.add('d-none');
            }
        });
    </script>
</body>
</html>
