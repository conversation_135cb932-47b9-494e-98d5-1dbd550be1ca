<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fyers Token Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            padding-top: 20px;
            padding-bottom: 40px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            font-weight: bold;
        }
        .token-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .token-value {
            font-family: monospace;
            word-break: break-all;
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin-top: 5px;
        }
        .alert {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark mb-4">
        <div class="container">
            <a class="navbar-brand" href="/">Options Analysis</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/options-analysis">Option Chain</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analyze-all">Analyze All</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/token-manager">Token Manager</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        <h1 class="mb-4">Fyers Token Manager</h1>

        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Manage Access Token</h5>
            </div>
            <div class="card-body">
                {% if token_exists and token_data %}
                <div class="token-info">
                    <h5>Current Token Information</h5>
                    <p><strong>Last Updated:</strong> {{ token_data.timestamp_readable }}</p>
                    <p><strong>Access Token:</strong></p>
                    <div class="token-value">{{ token_data.access_token }}</div>
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    No access token found. Please add your Fyers API access token below.
                </div>
                {% endif %}

                <!-- Generate Token from Auth Code Section -->
                <h5 class="mt-4">Generate Access Token (Recommended)</h5>
                <p class="text-muted">Generate a new access token using authorization code from Fyers API</p>

                <div class="mb-3">
                    <button type="button" class="btn btn-success" id="generateAuthBtn">
                        <i class="bi bi-key-fill me-2"></i>Step 1: Get Authorization Code
                    </button>
                    <small class="form-text text-muted d-block mt-1">This will open Fyers login page in a new tab</small>
                </div>

                <form id="authCodeForm">
                    <div class="mb-3">
                        <label for="authCode" class="form-label">Step 2: Enter Authorization Code</label>
                        <input type="text" class="form-control" id="authCode" placeholder="Paste the auth_code from the redirect URL" required>
                        <small class="form-text text-muted">After logging in, copy the 'auth_code' parameter from the redirect URL</small>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-gear-fill me-2"></i>Generate Access Token
                    </button>
                </form>

                <hr class="my-4">

                <!-- Manual Token Update Section -->
                <h5>Manual Token Update</h5>
                <p class="text-muted">If you already have an access token, you can update it directly</p>

                <form id="tokenForm">
                    <div class="mb-3">
                        <label for="accessToken" class="form-label">Access Token</label>
                        <textarea class="form-control" id="accessToken" rows="3" placeholder="Paste your Fyers API access token here" required></textarea>
                    </div>
                    <button type="submit" class="btn btn-secondary">Update Token</button>
                </form>

                <!-- Alerts -->
                <div class="alert alert-success mt-3 d-none" id="successAlert">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <span id="successMessage">Token updated successfully!</span>
                </div>
                <div class="alert alert-danger mt-3 d-none" id="errorAlert">
                    <i class="bi bi-x-circle-fill me-2"></i>
                    <span id="errorMessage">Error updating token.</span>
                </div>
                <div class="alert alert-info mt-3 d-none" id="infoAlert">
                    <i class="bi bi-info-circle-fill me-2"></i>
                    <span id="infoMessage">Processing...</span>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">How to Generate Access Token</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success">✅ Recommended Method</h6>
                        <ol class="mb-3">
                            <li class="mb-2">Click "Get Authorization Code" button above</li>
                            <li class="mb-2">Login with your Fyers credentials in the new tab</li>
                            <li class="mb-2">After login, you'll be redirected to a URL like:<br>
                                <code class="small">https://trade.fyers.in/api-login/redirect-to-app?auth_code=XXXXXXXXXX&state=sample_state</code>
                            </li>
                            <li class="mb-2">Copy the <strong>auth_code</strong> value from the URL</li>
                            <li>Paste it in the "Authorization Code" field and click "Generate Access Token"</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-secondary">Alternative Method</h6>
                        <ol class="mb-3">
                            <li class="mb-2">Go to <a href="https://myapi.fyers.in/" target="_blank">Fyers API Portal</a></li>
                            <li class="mb-2">Login with your credentials</li>
                            <li class="mb-2">Navigate to 'My Apps' section</li>
                            <li class="mb-2">Find app: <strong>B5QJCSZE9E-100</strong></li>
                            <li class="mb-2">Generate access token manually</li>
                            <li>Use "Manual Token Update" section above</li>
                        </ol>
                    </div>
                </div>

                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <strong>Important:</strong> Access tokens expire after 24 hours. You'll need to regenerate them daily.
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const tokenForm = document.getElementById('tokenForm');
            const authCodeForm = document.getElementById('authCodeForm');
            const generateAuthBtn = document.getElementById('generateAuthBtn');
            const successAlert = document.getElementById('successAlert');
            const errorAlert = document.getElementById('errorAlert');
            const infoAlert = document.getElementById('infoAlert');
            const successMessage = document.getElementById('successMessage');
            const errorMessage = document.getElementById('errorMessage');
            const infoMessage = document.getElementById('infoMessage');

            // Generate Authorization Code Button
            generateAuthBtn.addEventListener('click', function() {
                const authUrl = 'https://api-t1.fyers.in/api/v3/generate-authcode?client_id=B5QJCSZE9E-100&redirect_uri=https://trade.fyers.in/api-login/redirect-to-app&response_type=code&state=sample_state';

                showInfo('Opening Fyers authorization page...');

                // Open in new tab
                window.open(authUrl, '_blank');

                // Hide info after a few seconds
                setTimeout(() => {
                    hideAlerts();
                }, 3000);
            });

            // Auth Code Form Submission
            authCodeForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const authCode = document.getElementById('authCode').value.trim();

                if (!authCode) {
                    showError('Authorization code cannot be empty');
                    return;
                }

                hideAlerts();
                showInfo('Generating access token...');

                // Send the auth code to generate access token
                fetch('/generate-access-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ auth_code: authCode })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSuccess('Access token generated successfully!');
                        // Clear the auth code field
                        document.getElementById('authCode').value = '';
                        // Reload the page after a short delay to show the updated token
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    } else {
                        showError(data.message || 'Error generating access token');
                    }
                })
                .catch(error => {
                    showError('Network error: ' + error.message);
                });
            });

            // Manual Token Form Submission
            tokenForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const accessToken = document.getElementById('accessToken').value.trim();

                if (!accessToken) {
                    showError('Access token cannot be empty');
                    return;
                }

                hideAlerts();
                showInfo('Updating token...');

                // Send the token to the server
                fetch('/update-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `access_token=${encodeURIComponent(accessToken)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSuccess('Token updated successfully!');
                        // Reload the page after a short delay to show the updated token
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        showError(data.message || 'Error updating token');
                    }
                })
                .catch(error => {
                    showError('Network error: ' + error.message);
                });
            });

            function hideAlerts() {
                successAlert.classList.add('d-none');
                errorAlert.classList.add('d-none');
                infoAlert.classList.add('d-none');
            }

            function showSuccess(message) {
                hideAlerts();
                successMessage.textContent = message;
                successAlert.classList.remove('d-none');
            }

            function showError(message) {
                hideAlerts();
                errorMessage.textContent = message;
                errorAlert.classList.remove('d-none');
            }

            function showInfo(message) {
                hideAlerts();
                infoMessage.textContent = message;
                infoAlert.classList.remove('d-none');
            }
        });
    </script>
</body>
</html>
