<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fyers Manual Authentication</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .card-header {
            background-color: #0d6efd;
            color: white;
            border-radius: 10px 10px 0 0 !important;
            padding: 15px 20px;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        
        .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0a58ca;
        }
        
        .form-control {
            border-radius: 5px;
        }
        
        .alert {
            border-radius: 5px;
        }
        
        .steps {
            counter-reset: step-counter;
            list-style-type: none;
            padding-left: 0;
        }
        
        .steps li {
            position: relative;
            padding-left: 40px;
            margin-bottom: 15px;
        }
        
        .steps li::before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 0;
            width: 30px;
            height: 30px;
            background-color: #0d6efd;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .code-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            font-family: monospace;
            margin-bottom: 15px;
            word-break: break-all;
        }
        
        #tokenResult {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Fyers Manual Authentication</h1>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Authentication Steps</h5>
            </div>
            <div class="card-body">
                <ol class="steps">
                    <li>
                        <p>Click the button below to open the Fyers authentication page in a new tab.</p>
                        <a href="https://api-t1.fyers.in/api/v3/generate-authcode?client_id=9JGVOKPK31-100&redirect_uri=https%3A%2F%2Fwww.google.co.in%2F&response_type=code&state=None" target="_blank" class="btn btn-primary">Open Fyers Auth Page</a>
                    </li>
                    <li>
                        <p>Log in with your Fyers credentials and authorize the application.</p>
                    </li>
                    <li>
                        <p>After successful authorization, you will be redirected to Google. In the URL, you will find a parameter called <code>code</code>.</p>
                        <p>Copy the entire code value (it's a long string that looks like a JWT token).</p>
                    </li>
                    <li>
                        <p>Paste the authorization code in the field below and click "Generate Token".</p>
                        <div class="mb-3">
                            <label for="authCode" class="form-label">Authorization Code</label>
                            <textarea id="authCode" class="form-control" rows="5" placeholder="Paste your authorization code here"></textarea>
                        </div>
                        <button id="generateTokenBtn" class="btn btn-primary">Generate Token</button>
                    </li>
                </ol>
            </div>
        </div>
        
        <div id="loadingIndicator" class="d-none">
            <div class="d-flex justify-content-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
            <p class="text-center mt-2">Generating token...</p>
        </div>
        
        <div id="tokenResult" class="card">
            <div class="card-header">
                <h5 class="mb-0">Token Result</h5>
            </div>
            <div class="card-body">
                <div id="tokenAlert" class="alert" role="alert"></div>
                
                <div id="tokenDetails" class="d-none">
                    <h6>Access Token:</h6>
                    <div class="code-box" id="accessToken"></div>
                    
                    <p>The token has been saved and will be used for all Fyers API requests.</p>
                    
                    <a href="/fyers-option-chain" class="btn btn-primary">Go to Option Chain Viewer</a>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const authCodeInput = document.getElementById('authCode');
            const generateTokenBtn = document.getElementById('generateTokenBtn');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const tokenResult = document.getElementById('tokenResult');
            const tokenAlert = document.getElementById('tokenAlert');
            const tokenDetails = document.getElementById('tokenDetails');
            const accessTokenElement = document.getElementById('accessToken');
            
            // Pre-fill with the auth code if provided
            const defaultAuthCode = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcHBfaWQiOiI5SkdWT0tQSzMxIiwidXVpZCI6IjliMWZmNzZlODAxZDRlNmNiODI0MGI3ZDU5ZGVhOWFmIiwiaXBBZGRyIjoiIiwibm9uY2UiOiIiLCJzY29wZSI6IiIsImRpc3BsYXlfbmFtZSI6IkZBQTE2NTE1Iiwib21zIjoiSzEiLCJoc21fa2V5IjoiOThkMjQxMjFmNjUxZThhZjUwOGI5MjQ2OGUxNDA3NWE3MzU3Y2YzYjQwYzBmZTE5ZWEwZmQ3OWEiLCJpc0RkcGlFbmFibGVkIjoiTiIsImlzTXRmRW5hYmxlZCI6Ik4iLCJhdWQiOiJbXCJkOjFcIixcImQ6MlwiLFwieDowXCIsXCJ4OjFcIixcIng6MlwiXSIsImV4cCI6MTc0NTYyMzQ2NCwiaWF0IjoxNzQ1NTkzNDY0LCJpc3MiOiJhcGkubG9naW4uZnllcnMuaW4iLCJuYmYiOjE3NDU1OTM0NjQsInN1YiI6ImF1dGhfY29kZSJ9.M9mM_YnrhNvvOiJ5ITgH84qHbVDivc8gsd972CgM85A";
            authCodeInput.value = defaultAuthCode;
            
            generateTokenBtn.addEventListener('click', function() {
                const authCode = authCodeInput.value.trim();
                
                if (!authCode) {
                    showError('Please enter an authorization code');
                    return;
                }
                
                // Show loading indicator
                loadingIndicator.classList.remove('d-none');
                tokenResult.style.display = 'none';
                
                // Send request to generate token
                fetch('/fyers-generate-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `auth_code=${encodeURIComponent(authCode)}`
                })
                .then(response => response.json())
                .then(data => {
                    // Hide loading indicator
                    loadingIndicator.classList.add('d-none');
                    tokenResult.style.display = 'block';
                    
                    if (data.success) {
                        // Show success message
                        tokenAlert.classList.remove('alert-danger');
                        tokenAlert.classList.add('alert-success');
                        tokenAlert.textContent = data.message;
                        
                        // Show token details
                        tokenDetails.classList.remove('d-none');
                        accessTokenElement.textContent = data.token;
                    } else {
                        // Show error message
                        showError(data.message);
                        tokenDetails.classList.add('d-none');
                    }
                })
                .catch(error => {
                    // Hide loading indicator
                    loadingIndicator.classList.add('d-none');
                    tokenResult.style.display = 'block';
                    
                    // Show error message
                    showError('Error generating token: ' + error.message);
                    tokenDetails.classList.add('d-none');
                });
            });
            
            function showError(message) {
                tokenAlert.classList.remove('alert-success');
                tokenAlert.classList.add('alert-danger');
                tokenAlert.textContent = message;
            }
        });
    </script>
</body>
</html>
