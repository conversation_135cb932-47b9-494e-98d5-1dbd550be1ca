#!/usr/bin/env python3
"""
Decode and analyze the current token
"""

import json
import base64
from datetime import datetime

def decode_token_from_file():
    """Decode token from the token file"""
    try:
        with open('tokens/fyers_token.json', 'r') as f:
            token_data = json.load(f)
        
        token = token_data.get('access_token')
        if not token:
            print("❌ No token found in file")
            return
        
        print(f"🔍 Analyzing token: {token[:50]}...")
        
        # Check if token is JWT format
        if token.count('.') != 2:
            print("❌ Token is not in JWT format")
            return
        
        # Split the token
        header, payload, signature = token.split('.')
        
        # Decode header
        header += '=' * (4 - len(header) % 4)
        decoded_header = base64.b64decode(header)
        header_data = json.loads(decoded_header)
        print(f"📋 Header: {json.dumps(header_data, indent=2)}")
        
        # Decode payload
        payload += '=' * (4 - len(payload) % 4)
        decoded_payload = base64.b64decode(payload)
        payload_data = json.loads(decoded_payload)
        print(f"📋 Payload: {json.dumps(payload_data, indent=2)}")
        
        # Check token type
        token_type = payload_data.get('sub', 'unknown')
        print(f"\n🏷️  Token Type: {token_type}")
        
        if token_type == 'refresh_token':
            print("❌ This is a REFRESH TOKEN, not an ACCESS TOKEN!")
            print("   The API expects an access token for authentication.")
        elif token_type == 'access_token':
            print("✅ This is an ACCESS TOKEN")
        else:
            print(f"⚠️  Unknown token type: {token_type}")
        
        # Check expiry
        if 'exp' in payload_data:
            exp_timestamp = payload_data['exp']
            exp_date = datetime.fromtimestamp(exp_timestamp)
            current_date = datetime.now()
            
            print(f"📅 Token expires: {exp_date}")
            print(f"📅 Current time: {current_date}")
            
            if exp_date > current_date:
                print("✅ Token is not expired")
            else:
                print("❌ Token has expired")
        
        # Check other important fields
        if 'fy_id' in payload_data:
            print(f"👤 User ID: {payload_data['fy_id']}")
        
        if 'appType' in payload_data:
            print(f"📱 App Type: {payload_data['appType']}")
            
    except Exception as e:
        print(f"❌ Error decoding token: {str(e)}")

if __name__ == "__main__":
    print("🔍 Token Analysis")
    print("=" * 40)
    decode_token_from_file()
