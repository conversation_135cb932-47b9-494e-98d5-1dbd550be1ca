#!/usr/bin/env python3
"""
Fix token by generating proper access token
"""

import os
import json
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def generate_access_token_from_auth_code(auth_code):
    """Generate access token from auth code"""
    try:
        from fyers_apiv3 import fyersModel
        
        app_id = os.environ.get('FYERS_APP_ID')
        secret_id = os.environ.get('FYERS_SECRET_ID')
        
        if not app_id or not secret_id:
            print("❌ Missing FYERS_APP_ID or FYERS_SECRET_ID in environment")
            return None
        
        print(f"🔄 Generating access token...")
        print(f"📋 App ID: {app_id}")
        print(f"📋 Auth Code: {auth_code[:10]}...")
        
        # Create session model
        session = fyersModel.SessionModel(
            client_id=app_id,
            secret_key=secret_id,
            redirect_uri="https://trade.fyers.in/api-login/redirect-to-app",
            response_type="code",
            grant_type="authorization_code"
        )
        
        # Set the auth code
        session.set_token(auth_code)
        
        # Generate access token
        response = session.generate_token()
        
        if response and response.get('s') == 'ok':
            access_token = response.get('access_token')
            
            if access_token:
                print(f"✅ Access token generated: {access_token[:20]}...")
                
                # Save the token
                token_file = 'tokens/fyers_token.json'
                with open(token_file, 'w') as f:
                    json.dump({
                        "access_token": access_token,
                        "timestamp": time.time()
                    }, f)
                
                print(f"✅ Token saved to {token_file}")
                
                # Test the token
                test_token(access_token)
                
                return access_token
            else:
                print("❌ No access token in response")
                return None
        else:
            error_msg = response.get('message', 'Unknown error') if response else 'No response from API'
            print(f"❌ Token generation failed: {error_msg}")
            print(f"📋 Full response: {response}")
            return None
            
    except Exception as e:
        print(f"❌ Error generating access token: {str(e)}")
        return None

def test_token(access_token):
    """Test the generated access token"""
    try:
        from fyers_apiv3 import fyersModel
        
        app_id = os.environ.get('FYERS_APP_ID')
        
        # Create client with new token
        fyers = fyersModel.FyersModel(
            client_id=app_id,
            token=access_token,
            is_async=False,
            log_path=""
        )
        
        print("🔄 Testing new access token...")
        
        # Test profile API
        response = fyers.get_profile()
        
        if response and response.get('s') == 'ok':
            print("✅ New access token is working!")
            profile_data = response.get('data', {})
            print(f"📊 User ID: {profile_data.get('fy_id', 'Unknown')}")
            print(f"📊 Display Name: {profile_data.get('display_name', 'Unknown')}")
            return True
        else:
            print(f"❌ New access token test failed: {response}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing new token: {str(e)}")
        return False

def main():
    """Main function"""
    print("🔧 Fyers Token Fix Utility")
    print("=" * 40)
    
    print("To fix the token issue, you need an authorization code.")
    print("Get it from: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=B5QJCSZE9E-100&redirect_uri=https://trade.fyers.in/api-login/redirect-to-app&response_type=code&state=sample_state")
    print()
    
    auth_code = input("Enter the authorization code: ").strip()
    
    if not auth_code:
        print("❌ No authorization code provided")
        return
    
    access_token = generate_access_token_from_auth_code(auth_code)
    
    if access_token:
        print("\n🎉 Token fix completed successfully!")
        print("You can now use the application without token errors.")
    else:
        print("\n❌ Token fix failed. Please try again or use the web interface.")

if __name__ == "__main__":
    main()
